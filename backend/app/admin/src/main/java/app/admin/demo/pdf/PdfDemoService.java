//package app.admin.demo.pdf;
//
//import infra.report.pdf.core.PdfRenderOptions;
//import infra.report.pdf.core.PdfReportService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.stereotype.Service;
//import org.thymeleaf.TemplateEngine;
//import org.thymeleaf.context.Context;
//
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.LinkedHashMap;
//import java.util.Map;
//
///**
// * <AUTHOR>
// */
//@Service
//@RequiredArgsConstructor
//public class PdfDemoService {
//
//    private final PdfReportService pdfReportService;
//    private final TemplateEngine templateEngine;
//
//    public byte[] createDemoPdf() {
//        // 1. 准备数据
//        Map<String, Object> data = new LinkedHashMap<>();
//        data.put("userName", "Gemini");
//        data.put("reportDate", LocalDate.now().format(DateTimeFormatter.ISO_DATE));
//
//        Map<String, String> details = new LinkedHashMap<>();
//        details.put("功能点 1", "页眉/页脚支持");
//        details.put("功能点 2", "页码生成");
//        details.put("功能点 3", "外部 CSS 引入");
//        details.put("功能点 4", "图片 logo 展示");
//        details.put("功能点 5", "动态表格生成");
//        details.put("功能点 6", "中文和水印");
//        data.put("details", details.entrySet());
//
//        // 2. 渲染 HTML
//        Context context = new Context();
//        context.setVariables(data);
//        String htmlContent = templateEngine.process("pdf/demo_template", context);
//
//        // 3. 定义渲染选项
//        PdfRenderOptions options = PdfRenderOptions.builder()
//                .watermarkText("内部机密文件")
//                .headerEnabled(true)
//                .footerEnabled(true)
//                .build();
//
//        // 4. 生成 PDF
//        return pdfReportService.generatePdfFromHtml(htmlContent, options);
//    }
//}

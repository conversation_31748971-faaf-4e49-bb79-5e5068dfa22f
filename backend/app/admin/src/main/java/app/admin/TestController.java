package app.admin;

import infra.report.code.CodeRenderOptions;
import infra.report.code.CodeUtil;
import infra.report.pdf.PageSize;
import infra.report.pdf.PdfRenderOptions;
import infra.report.pdf.PdfUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URI;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @GetMapping("/qrcode")
    public void generateQRCode(
            @RequestParam String data,
            HttpServletResponse response
    ) throws IOException {
        var config= CodeRenderOptions.defaultQRCode();
        BufferedImage logoImage = ImageIO.read(URI.create("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png").toURL());
        config.autoConfigLogo(logoImage);
        CodeUtil.generateQRCodeToResponse(data, config, response);
    }

    @GetMapping("/pdf")
    public ResponseEntity<byte[]> pdf() {
        String html = """
                <!DOCTYPE html>
                                   <html>
                                   <head>
                                       <meta charset="UTF-8">
                                       <title>基础示例</title>
                                       <style>
                                           body {
                                               font-family: SimHei, SimSun, Microsoft YaHei, SansSerif, sans-serif;
                                               font-size: 12pt;
                                           }
                                           h1 { color: #2c3e50; text-align: center; }
                                           table { width: 100%; border-collapse: collapse; }
                                           th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                           th { background-color: #f2f2f2; }
                                       </style>
                                   </head>
                                   <body>
                                       <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                   </body>
                                   </html>
            """;

        // 配置PDF选项，包括水印和页眉页脚
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .imagePath("http://127.0.0.1:8080/test/qrcode?data=ruson")
                        .color("#cccccc")
                        .opacity(0.3f)
                        .rotation(-45f)
                        .fontSize(48f)
                        .position("center")
                        .repeatWatermark(true)           // 启用循环水印
                        .horizontalSpacing(200f)         // 水平间距
                        .verticalSpacing(150f)           // 垂直间距
                        .density(0.5f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("Test Report")
                        .footerLeft("生成时间: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
                        .footerRight("第 {page} 页，共 {total} 页")
                        .showPageNumbers(true)
                        .fontSize(15f)
                        .build())
                .build();

        // PdfUtil会自动获取baseUrl
        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        // headers.setContentDispositionFormData("attachment", "demo-report.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/repeat-watermark-test")
    public ResponseEntity<byte[]> repeatWatermarkTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>循环水印测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        .page-break { page-break-before: always; }
                        h1 { color: #2c3e50; }
                        .content { margin: 20px 0; }
                        .demo-section {
                            margin: 30px 0;
                            padding: 20px;
                            border: 1px solid #ddd;
                            background-color: #f9f9f9;
                        }
                    </style>
                </head>
                <body>
                    <h1>循环水印测试页面</h1>

                    <div class="demo-section">
                        <h2>测试说明</h2>
                        <p>本页面用于测试循环水印功能，您应该能看到：</p>
                        <ul>
                            <li>整个页面布满"CONFIDENTIAL"水印文字</li>
                            <li>水印以网格形式重复出现</li>
                            <li>水印透明度为30%，倾斜-45度</li>
                            <li>水印密度可以通过density参数调节</li>
                        </ul>
                    </div>

                    <div class="demo-section">
                        <h2>内容区域1</h2>
                        <p>这里是一些正常的文档内容。水印应该覆盖在内容之上，但不影响内容的可读性。</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                    </div>

                    <div class="demo-section">
                        <h2>内容区域2</h2>
                        <p>更多的文档内容用于测试水印效果。水印应该均匀分布在整个页面上。</p>
                        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                    </div>

                    <div class="page-break">
                        <h1>第二页 - 继续测试</h1>
                        <div class="demo-section">
                            <h2>多页测试</h2>
                            <p>这是第二页，用于验证循环水印在多页文档中的效果。</p>
                            <p>每一页都应该有相同的循环水印效果。</p>
                        </div>
                    </div>
                </body>
                </html>
                """;

        // 配置循环水印
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("CONFIDENTIAL")
                        .color("#FF6B6B")
                        .opacity(0.3f)
                        .rotation(-45f)
                        .fontSize(36f)
                        .position("center")
                        .repeatWatermark(true)           // 启用循环水印
                        .horizontalSpacing(200f)         // 水平间距200像素
                        .verticalSpacing(150f)           // 垂直间距150像素
                        .density(1.0f)                   // 正常密度
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("循环水印测试报告")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "repeat-watermark-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/dense-watermark-test")
    public ResponseEntity<byte[]> denseWatermarkTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>密集水印测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .content { margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>密集水印测试</h1>
                    <div class="content">
                        <p>本页面测试高密度水印效果（density=2.0）</p>
                        <p>水印应该更加密集地分布在页面上。</p>
                        <p>这可以用于需要更强保护效果的文档。</p>
                    </div>
                </body>
                </html>
                """;

        // 配置密集水印
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("DRAFT")
                        .color("#CCCCCC")
                        .opacity(0.2f)
                        .rotation(-30f)
                        .fontSize(24f)
                        .repeatWatermark(true)
                        .horizontalSpacing(150f)
                        .verticalSpacing(100f)
                        .density(2.0f)                   // 密集度2倍
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "dense-watermark-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/sparse-watermark-test")
    public ResponseEntity<byte[]> sparseWatermarkTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>稀疏水印测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .content { margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <h1>稀疏水印测试</h1>
                    <div class="content">
                        <p>本页面测试稀疏水印效果（density=0.5）</p>
                        <p>水印分布更加稀疏，对内容的干扰更小。</p>
                        <p>适用于需要保持良好可读性的文档。</p>
                    </div>
                </body>
                </html>
                """;

        // 配置稀疏水印
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("SAMPLE")
                        .color("#999999")
                        .opacity(0.4f)
                        .rotation(45f)
                        .fontSize(48f)
                        .repeatWatermark(true)
                        .horizontalSpacing(300f)
                        .verticalSpacing(200f)
                        .density(0.5f)                   // 稀疏度0.5倍
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "sparse-watermark-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/a3-test")
    public ResponseEntity<byte[]> a3Test() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>A3纸张测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; text-align: center; }
                        .page-info {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .content-grid {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 30px;
                            margin: 30px 0;
                        }
                        .content-box {
                            border: 2px solid #ddd;
                            padding: 20px;
                            border-radius: 8px;
                        }
                        .size-demo {
                            width: 100%;
                            height: 200px;
                            border: 1px solid #ccc;
                            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
                            background-size: 20px 20px;
                            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 24px;
                            font-weight: bold;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <h1>A3纸张大小测试页面</h1>

                    <div class="page-info">
                        <h2>页面信息</h2>
                        <ul>
                            <li><strong>纸张大小：</strong>A3 (297 × 420 mm)</li>
                            <li><strong>测试目的：</strong>验证A3纸张的PDF生成效果</li>
                            <li><strong>水印效果：</strong>循环水印覆盖整个A3页面</li>
                            <li><strong>页眉页脚：</strong>适配A3纸张宽度</li>
                        </ul>
                    </div>

                    <div class="content-grid">
                        <div class="content-box">
                            <h3>左侧内容区域</h3>
                            <p>这是A3纸张左侧的内容区域。A3纸张比A4更宽，可以容纳更多的内容。</p>
                            <div class="size-demo">A3 左侧</div>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                        </div>

                        <div class="content-box">
                            <h3>右侧内容区域</h3>
                            <p>这是A3纸张右侧的内容区域。更大的页面空间允许更灵活的布局设计。</p>
                            <div class="size-demo">A3 右侧</div>
                            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                        </div>
                    </div>

                    <div class="page-info">
                        <h2>A3纸张优势</h2>
                        <ul>
                            <li>更大的显示空间，适合图表和表格</li>
                            <li>可以容纳更多的并列内容</li>
                            <li>适合技术文档和设计图纸</li>
                            <li>水印覆盖面积更大，保护效果更好</li>
                        </ul>
                    </div>
                </body>
                </html>
                """;

        // 配置A3纸张和循环水印
        PdfRenderOptions options = PdfRenderOptions.builder()
                .pageSize(PageSize.A3)                  // 设置A3纸张
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("A3 DOCUMENT")
                        .color("#CCCCCC")
                        .opacity(0.2f)
                        .rotation(-30f)
                        .fontSize(32f)
                        .repeatWatermark(true)
                        .horizontalSpacing(250f)         // A3纸张适配的间距
                        .verticalSpacing(180f)
                        .density(1.0f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("A3纸张测试报告")
                        .footerLeft("A3 (297×420mm)")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .fontSize(12f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "a3-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/page-size-comparison")
    public ResponseEntity<byte[]> pageSizeComparison() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>纸张大小对比</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 30px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; text-align: center; }
                        .size-info {
                            background-color: #e8f4fd;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                            border-left: 4px solid #007bff;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 12px;
                            text-align: left;
                        }
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                        }
                        .current-size {
                            background-color: #fff3cd;
                            font-weight: bold;
                        }
                    </style>
                </head>
                <body>
                    <h1>PDF纸张大小支持</h1>

                    <div class="size-info">
                        <h2>当前页面：A4纸张</h2>
                        <p>本页面使用A4纸张大小生成，用于对比不同纸张规格的效果。</p>
                    </div>

                    <h2>支持的纸张规格</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>纸张类型</th>
                                <th>尺寸 (mm)</th>
                                <th>尺寸 (inch)</th>
                                <th>适用场景</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>A5</td>
                                <td>148 × 210</td>
                                <td>5.8 × 8.3</td>
                                <td>小册子、便签</td>
                            </tr>
                            <tr class="current-size">
                                <td>A4</td>
                                <td>210 × 297</td>
                                <td>8.3 × 11.7</td>
                                <td>标准文档、报告</td>
                            </tr>
                            <tr>
                                <td>A3</td>
                                <td>297 × 420</td>
                                <td>11.7 × 16.5</td>
                                <td>图表、设计图</td>
                            </tr>
                            <tr>
                                <td>Letter</td>
                                <td>216 × 279</td>
                                <td>8.5 × 11</td>
                                <td>北美标准文档</td>
                            </tr>
                            <tr>
                                <td>Legal</td>
                                <td>216 × 356</td>
                                <td>8.5 × 14</td>
                                <td>法律文件</td>
                            </tr>
                        </tbody>
                    </table>

                    <h2>使用方法</h2>
                    <pre><code>PdfRenderOptions options = PdfRenderOptions.builder()
    .pageSize(PageSize.A3)  // 设置纸张大小
    .build();</code></pre>
                </body>
                </html>
                """;

        // 使用A4纸张作为对比
        PdfRenderOptions options = PdfRenderOptions.builder()
                .pageSize(PageSize.A4)
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("A4 REFERENCE")
                        .color("#999999")
                        .opacity(0.15f)
                        .rotation(45f)
                        .fontSize(28f)
                        .repeatWatermark(true)
                        .horizontalSpacing(200f)
                        .verticalSpacing(150f)
                        .density(0.8f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("纸张大小对比文档")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "page-size-comparison.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }
}

package app.admin;

import infra.report.code.CodeRenderOptions;
import infra.report.code.CodeUtil;
import infra.report.pdf.PdfRenderOptions;
import infra.report.pdf.PdfUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @GetMapping("/qrcode")
    public void generateQRCode(
            @RequestParam String data,
            HttpServletResponse response
    ) throws IOException {
        var config= CodeRenderOptions.defaultQRCode();
        BufferedImage logoImage = ImageIO.read(URI.create("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png").toURL());
        config.autoConfigLogo(logoImage);
        CodeUtil.generateQRCodeToResponse(data, config, response);
    }

    @GetMapping("/pdf")
    public ResponseEntity<byte[]> pdf() {
        String html = """
                <!DOCTYPE html>
                                   <html>
                                   <head>
                                       <meta charset="UTF-8">
                                       <title>基础示例</title>
                                       <style>
                                           body {
                                               font-family: SimHei, SimSun, Microsoft YaHei, SansSerif, sans-serif;
                                               font-size: 12pt;
                                           }
                                           h1 { color: #2c3e50; text-align: center; }
                                           table { width: 100%; border-collapse: collapse; }
                                           th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                           th { background-color: #f2f2f2; }
                                       </style>
                                   </head>
                                   <body>
                                       <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:30px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                   </body>
                                   </html>
            """;

        // 配置PDF选项，包括水印和页眉页脚
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .imagePath("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png")
                        .color("#cccccc")
                        .opacity(0.3f)
                        .rotation(-45f)
                        .fontSize(48f)
                        .position("center")
                        .repeatWatermark(true)           // 启用循环水印
                        .horizontalSpacing(200f)         // 水平间距
                        .verticalSpacing(150f)           // 垂直间距
                        .density(0.5f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerLeft("机密文档")
                        .headerLeftColor("#FF0000")
                        .headerCenter("Test Report")
                        .headerCenterColor("#0066CC")
                        .headerRight("DRAFT")
                        .headerRightColor("#FF6600")
                        .footerLeft("生成时间: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .showPageNumbers(true)
                        .fontSize(15f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        // headers.setContentDispositionFormData("attachment", "demo-report.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/font-cache-test")
    public ResponseEntity<byte[]> fontCacheTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>字体缓存测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .test-info {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                    </style>
                </head>
                <body>
                    <h1>字体缓存性能测试</h1>

                    <div class="test-info">
                        <h2>测试说明</h2>
                        <p>本测试用于验证字体缓存机制的性能优化效果：</p>
                        <ul>
                            <li>首次加载：系统会扫描并加载字体</li>
                            <li>后续请求：直接使用缓存，提高性能</li>
                            <li>自定义字体目录：只加载一次</li>
                            <li>系统字体：全局缓存，避免重复扫描</li>
                        </ul>
                    </div>

                    <div class="test-info">
                        <h2>中文字体测试</h2>
                        <p>这里是中文内容测试：你好世界！</p>
                        <p>测试各种中文字符：汉字、标点符号、数字123。</p>
                    </div>

                    <div class="test-info">
                        <h2>性能优化</h2>
                        <p>优化后的字体加载机制：</p>
                        <ol>
                            <li>使用缓存避免重复加载</li>
                            <li>按需加载自定义字体目录</li>
                            <li>系统字体只加载一次</li>
                            <li>线程安全的加载机制</li>
                        </ol>
                    </div>
                </body>
                </html>
                """;

        long startTime = System.currentTimeMillis();

        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("FONT TEST")
                        .color("#CCCCCC")
                        .opacity(0.2f)
                        .rotation(45f)
                        .fontSize(32f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("字体缓存测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("生成时间: " + (System.currentTimeMillis() - startTime) + "ms")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        long endTime = System.currentTimeMillis();
        log.info("字体缓存测试完成，耗时: {}ms", endTime - startTime);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "font-cache-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/resource-font-test")
    public ResponseEntity<byte[]> resourceFontTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>资源字体测试</title>
                    <style>
                        body {
                            font-family: 'simhei', sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .font-test {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .chinese-text {
                            font-size: 16px;
                            margin: 10px 0;
                        }
                    </style>
                </head>
                <body>
                    <h1>资源字体测试页面</h1>

                    <div class="font-test">
                        <h2>中文字体测试</h2>
                        <div class="chinese-text">这是使用资源字体的中文测试文本。</div>
                        <div class="chinese-text">测试各种中文字符：你好世界！</div>
                        <div class="chinese-text">数字和符号：123456789，。；：""''</div>
                        <div class="chinese-text">繁体中文：繁體中文測試內容</div>
                    </div>

                    <div class="font-test">
                        <h2>字体加载说明</h2>
                        <ul>
                            <li>优先使用resources/fonts/目录下的字体文件</li>
                            <li>支持SimHei.ttf、SimSun.ttf等常见中文字体</li>
                            <li>如果配置了外部字体目录，会同时加载</li>
                            <li>适用于JAR包和Docker部署</li>
                        </ul>
                    </div>

                    <div class="font-test">
                        <h2>部署优势</h2>
                        <ol>
                            <li>字体文件打包在JAR中，无需外部依赖</li>
                            <li>Docker镜像包含字体，开箱即用</li>
                            <li>支持外部字体目录扩展</li>
                            <li>自动字体回退机制</li>
                        </ol>
                    </div>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")  // 使用资源字体
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("资源字体")
                        .color("#CCCCCC")
                        .opacity(0.2f)
                        .rotation(45f)
                        .fontSize(32f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("资源字体测试报告")
                        .headerCenterColor("#0066CC")
                        .footerLeft("使用内置字体")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "resource-font-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/optimized-font-test")
    public ResponseEntity<byte[]> optimizedFontTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>优化字体测试</title>
                    <style>
                        body {
                            font-family: 'simhei', sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .test-section {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                    </style>
                </head>
                <body>
                    <h1>优化后的字体处理测试</h1>

                    <div class="test-section">
                        <h2>字体处理优化</h2>
                        <ul>
                            <li>统一的字体缓存管理</li>
                            <li>自动扫描资源字体目录</li>
                            <li>简洁优雅的字体加载逻辑</li>
                            <li>高性能的字体初始化</li>
                        </ul>
                    </div>

                    <div class="test-section">
                        <h2>中文字体测试</h2>
                        <p>这是中文内容测试：你好世界！</p>
                        <p>繁体中文：繁體中文測試內容</p>
                        <p>特殊符号：①②③④⑤⑥⑦⑧⑨⑩</p>
                    </div>

                    <div class="test-section">
                        <h2>性能优化特性</h2>
                        <ol>
                            <li>字体只初始化一次，全局缓存</li>
                            <li>智能扫描，避免硬编码字体列表</li>
                            <li>统一的字体管理，支持HTML和页眉页脚</li>
                            <li>优雅的错误处理和降级机制</li>
                        </ol>
                    </div>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("优化字体")
                        .color("#DDDDDD")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(36f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("字体优化测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("统一字体管理")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "optimized-font-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/font-debug")
    public ResponseEntity<String> fontDebug() {
        StringBuilder debug = new StringBuilder();
        debug.append("=== 字体调试信息 ===\n\n");

        try {
            // 检查资源字体目录
            debug.append("1. 检查资源字体目录:\n");
            InputStream resourceStream = getClass().getClassLoader().getResourceAsStream("fonts");
            if (resourceStream != null) {
                resourceStream.close();
                debug.append("   ✓ resources/fonts 目录存在\n");

                // 列出资源字体文件
                String[] fontFiles = {"simhei.ttf", "simsun.ttc", "SimHei.ttf", "SimSun.ttf"};
                for (String fontFile : fontFiles) {
                    InputStream fontStream = getClass().getClassLoader().getResourceAsStream("fonts/" + fontFile);
                    if (fontStream != null) {
                        fontStream.close();
                        debug.append("   ✓ 发现字体文件: ").append(fontFile).append("\n");
                    } else {
                        debug.append("   ✗ 未找到字体文件: ").append(fontFile).append("\n");
                    }
                }
            } else {
                debug.append("   ✗ resources/fonts 目录不存在\n");
            }

            debug.append("\n2. 临时目录信息:\n");
            String tempDir = System.getProperty("java.io.tmpdir");
            debug.append("   临时目录: ").append(tempDir).append("\n");

            File tempDirFile = new File(tempDir);
            debug.append("   临时目录可写: ").append(tempDirFile.canWrite()).append("\n");

            debug.append("\n3. 测试字体提取:\n");
            // 测试提取一个字体文件
            InputStream testStream = getClass().getClassLoader().getResourceAsStream("fonts/simhei.ttf");
            if (testStream != null) {
                byte[] fontData = testStream.readAllBytes();
                testStream.close();
                debug.append("   ✓ simhei.ttf 读取成功，大小: ").append(fontData.length).append(" 字节\n");

                // 尝试写入临时文件
                String testFontPath = tempDir + File.separator + "test-simhei.ttf";
                try (FileOutputStream fos = new FileOutputStream(testFontPath)) {
                    fos.write(fontData);
                    fos.flush();
                }

                File testFile = new File(testFontPath);
                if (testFile.exists()) {
                    debug.append("   ✓ 临时文件创建成功: ").append(testFontPath).append("\n");
                    debug.append("   ✓ 临时文件大小: ").append(testFile.length()).append(" 字节\n");
                    testFile.delete(); // 清理测试文件
                } else {
                    debug.append("   ✗ 临时文件创建失败\n");
                }
            } else {
                debug.append("   ✗ simhei.ttf 读取失败\n");
            }

        } catch (Exception e) {
            debug.append("\n错误: ").append(e.getMessage()).append("\n");
            debug.append("堆栈: ").append(java.util.Arrays.toString(e.getStackTrace())).append("\n");
        }

        return ResponseEntity.ok()
                .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
                .body(debug.toString());
    }

    @GetMapping("/simple-font-test")
    public ResponseEntity<byte[]> simpleFontTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>简单字体测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .chinese { font-family: 'simhei', 'SimHei', sans-serif; }
                    </style>
                </head>
                <body>
                    <h1>Simple Font Test</h1>
                    <p>English text should display normally.</p>
                    <p class="chinese">中文测试：你好世界！</p>
                    <p class="chinese">Chinese test: 这是中文内容测试</p>
                </body>
                </html>
                """;

        // 使用最简单的配置
        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "simple-font-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/font-fix-test")
    public ResponseEntity<byte[]> fontFixTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>字体修复测试</title>
                    <style>
                        body {
                            font-family: 'simhei', 'SimHei', 'simsun', 'SimSun', sans-serif !important;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 {
                            color: #2c3e50;
                            font-family: 'simhei', 'SimHei', sans-serif !important;
                        }
                        .chinese {
                            font-family: 'simhei', 'SimHei', 'simsun', 'SimSun', sans-serif !important;
                            font-size: 16px;
                            margin: 10px 0;
                        }
                        .test-box {
                            border: 1px solid #ddd;
                            padding: 20px;
                            margin: 20px 0;
                            background-color: #f9f9f9;
                        }
                    </style>
                </head>
                <body>
                    <h1>字体修复测试页面</h1>

                    <div class="test-box">
                        <h2>英文测试</h2>
                        <p>English text should display normally with fallback fonts.</p>
                        <p>Numbers: 1234567890</p>
                        <p>Symbols: !@#$%^&*()</p>
                    </div>

                    <div class="test-box">
                        <h2 class="chinese">中文测试</h2>
                        <p class="chinese">这是中文内容测试：你好世界！</p>
                        <p class="chinese">简体中文：测试各种中文字符显示效果</p>
                        <p class="chinese">繁体中文：繁體中文測試內容顯示</p>
                        <p class="chinese">数字符号：123456789，。；：""''</p>
                    </div>

                    <div class="test-box">
                        <h2>字体状态</h2>
                        <ul>
                            <li>字体目录注册：成功</li>
                            <li>字体文件：simhei.ttf, simsun.ttc</li>
                            <li>CSS字体族：多重回退机制</li>
                            <li>强制字体：!important 声明</li>
                        </ul>
                    </div>
                </body>
                </html>
                """;

        // 使用明确的字体配置
        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("字体测试")
                        .color("#EEEEEE")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(32f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("字体修复测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("字体状态：已注册")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "font-fix-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/font-names-debug")
    public ResponseEntity<String> fontNamesDebug() {
        StringBuilder debug = new StringBuilder();
        debug.append("=== 字体名称调试 ===\n\n");

        try {
            // 检查临时目录中的字体文件
            String tempDir = System.getProperty("java.io.tmpdir");
            debug.append("临时目录: ").append(tempDir).append("\n\n");

            File tempDirFile = new File(tempDir);
            File[] fontFiles = tempDirFile.listFiles((dir, name) ->
                name.toLowerCase().endsWith(".ttf") ||
                name.toLowerCase().endsWith(".ttc") ||
                name.toLowerCase().endsWith(".otf"));

            if (fontFiles != null && fontFiles.length > 0) {
                debug.append("发现的字体文件:\n");
                for (File fontFile : fontFiles) {
                    debug.append("  - ").append(fontFile.getName())
                         .append(" (").append(fontFile.length()).append(" 字节)\n");

                    // 尝试读取字体信息
                    try {
                        com.lowagie.text.pdf.BaseFont baseFont = com.lowagie.text.pdf.BaseFont.createFont(
                            fontFile.getAbsolutePath(),
                            com.lowagie.text.pdf.BaseFont.IDENTITY_H,
                            com.lowagie.text.pdf.BaseFont.EMBEDDED
                        );

                        String[][] fontNames = baseFont.getFullFontName();
                        debug.append("    字体名称信息:\n");
                        for (String[] nameInfo : fontNames) {
                            if (nameInfo.length >= 4) {
                                debug.append("      ").append(nameInfo[3]).append("\n");
                            }
                        }

                    } catch (Exception e) {
                        debug.append("    读取字体信息失败: ").append(e.getMessage()).append("\n");
                    }
                }
            } else {
                debug.append("临时目录中没有找到字体文件\n");
            }

            debug.append("\n=== CSS字体测试 ===\n");
            debug.append("建议的CSS字体族名称:\n");
            debug.append("  font-family: 'simhei', 'SimHei', 'simsun', 'SimSun', sans-serif;\n");
            debug.append("  font-family: 'SimHei', 'simhei', sans-serif;\n");
            debug.append("  font-family: 'SimSun', 'simsun', serif;\n");

        } catch (Exception e) {
            debug.append("错误: ").append(e.getMessage()).append("\n");
        }

        return ResponseEntity.ok()
                .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
                .body(debug.toString());
    }

    @GetMapping("/final-chinese-test")
    public ResponseEntity<byte[]> finalChineseTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>最终中文测试</title>
                </head>
                <body>
                    <h1>最终中文字体测试</h1>

                    <h2>简体中文测试</h2>
                    <p>你好世界！这是简体中文测试内容。</p>
                    <p>数字：1234567890</p>
                    <p>标点：，。；：""''！？</p>

                    <h2>繁体中文测试</h2>
                    <p>繁體中文測試內容顯示效果。</p>

                    <h2>混合内容测试</h2>
                    <p>English and 中文 mixed content test.</p>
                    <p>Numbers 123 and 中文数字 一二三四五</p>

                    <h2>特殊字符测试</h2>
                    <p>©®™€¥£$¢</p>
                    <p>①②③④⑤⑥⑦⑧⑨⑩</p>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("中文页眉测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("中文页脚")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("中文水印")
                        .color("#DDDDDD")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(36f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "final-chinese-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }
}

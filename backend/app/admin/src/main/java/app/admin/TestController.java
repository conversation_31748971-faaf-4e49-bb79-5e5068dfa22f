package app.admin;

import infra.report.code.CodeRenderOptions;
import infra.report.code.CodeUtil;
import infra.report.pdf.FontDebugger;
import infra.report.pdf.FontManager;
import infra.report.pdf.PdfRenderOptions;
import infra.report.pdf.PdfUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @GetMapping("/qrcode")
    public void generateQRCode(
            @RequestParam String data,
            HttpServletResponse response
    ) throws IOException {
        var config= CodeRenderOptions.defaultQRCode();
        BufferedImage logoImage = ImageIO.read(new URL("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png"));
        config.autoConfigLogo(logoImage);
        CodeUtil.generateQRCodeToResponse(data, config, response);
    }

    @GetMapping("/pdf")
    public ResponseEntity<byte[]> pdf(HttpServletRequest request) {
        String html = """
                <!DOCTYPE html>
                                   <html>
                                   <head>
                                       <meta charset="UTF-8">
                                       <title>基础示例</title>
                                       <style>
                                           body {
                                               font-family: SimHei, SimSun, Microsoft YaHei, SansSerif, sans-serif;
                                               font-size: 12pt;
                                           }
                                           h1 { color: #2c3e50; text-align: center; }
                                           table { width: 100%; border-collapse: collapse; }
                                           th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                           th { background-color: #f2f2f2; }
                                       </style>
                                   </head>
                                   <body>
                                       <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="20" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                   </body>
                                   </html>
            """;

        // 构建baseUrl，用于解析相对路径的图片
        String baseUrl = request.getScheme() + "://" +
                        request.getServerName() + ":" +
                        request.getServerPort() +
                        request.getContextPath();

        log.info("PDF生成使用的baseUrl: {}", baseUrl);

        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("机密文档")
                        .color("#FF6B6B")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(48f)
                        .position("center")
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        // headers.setContentDispositionFormData("attachment", "demo-report.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/fonts")
    public ResponseEntity<Object> checkFonts() {
        FontManager fontManager = FontManager.getInstance();

        // 强制初始化字体管理器
        fontManager.initialize();

        return ResponseEntity.ok(fontManager.getFontStatistics());
    }

    @GetMapping("/pdf-font-test")
    public ResponseEntity<byte[]> pdfFontTest(HttpServletRequest request) {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>字体测试</title>
                    <style>
                        body {
                            font-family: 'SimHei', 'Microsoft YaHei', 'SimSun', sans-serif;
                            padding: 20px;
                            line-height: 1.6;
                        }
                        .font-test {
                            margin: 20px 0;
                            padding: 10px;
                            border: 1px solid #ccc;
                        }
                        .font-simhei { font-family: 'SimHei', sans-serif; }
                        .font-simsun { font-family: 'SimSun', serif; }
                        .font-yahei { font-family: 'Microsoft YaHei', sans-serif; }
                        .font-arial { font-family: 'Arial', sans-serif; }
                    </style>
                </head>
                <body>
                    <h1>PDF字体测试报告</h1>
                    <p>这是一个字体测试文档，用于验证中文字体是否正确显示。</p>

                    <div class="font-test">
                        <h3>默认字体测试</h3>
                        <p>中文测试：你好世界！这是中文内容测试。</p>
                        <p>English Test: Hello World! This is English content test.</p>
                        <p>数字测试：1234567890</p>
                        <p>特殊字符：！@#￥%……&*（）——+</p>
                    </div>

                    <div class="font-test font-simhei">
                        <h3>SimHei 黑体测试</h3>
                        <p>中文测试：你好世界！这是黑体字测试。</p>
                        <p>English: Hello World in SimHei font.</p>
                    </div>

                    <div class="font-test font-simsun">
                        <h3>SimSun 宋体测试</h3>
                        <p>中文测试：你好世界！这是宋体字测试。</p>
                        <p>English: Hello World in SimSun font.</p>
                    </div>

                    <div class="font-test font-yahei">
                        <h3>Microsoft YaHei 微软雅黑测试</h3>
                        <p>中文测试：你好世界！这是微软雅黑字测试。</p>
                        <p>English: Hello World in Microsoft YaHei font.</p>
                    </div>

                    <div class="font-test font-arial">
                        <h3>Arial 字体测试（英文）</h3>
                        <p>中文测试：你好世界！（如果显示为方块，说明Arial不支持中文）</p>
                        <p>English: Hello World in Arial font.</p>
                    </div>

                    <h2>字体状态信息</h2>
                    <p>如果上面的中文显示为方块或问号，说明对应的字体未正确加载。</p>
                    <p>请检查字体文件是否存在于指定目录中。</p>
                </body>
                </html>
                """;

        // 构建baseUrl
        String baseUrl = request.getScheme() + "://" +
                        request.getServerName() + ":" +
                        request.getServerPort() +
                        request.getContextPath();

        byte[] pdfBytes = PdfUtil.htmlToPdfWithBaseUrl(html, baseUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "font-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/simple-chinese-test")
    public ResponseEntity<byte[]> simpleChinese(HttpServletRequest request) {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>简单中文测试</title>
                    <style>
                        body {
                            font-family: SimHei;
                            font-size: 14pt;
                            padding: 20px;
                        }
                        .test1 { font-family: SimHei; }
                        .test2 { font-family: SimSun; }
                        .test3 { font-family: SansSerif; }
                        .test4 { font-family: Serif; }
                    </style>
                </head>
                <body>
                    <h1>中文字体测试</h1>
                    <p>默认字体：你好世界！Hello World! 123456</p>
                    <p class="test1">SimHei：你好世界！Hello World! 123456</p>
                    <p class="test2">SimSun：你好世界！Hello World! 123456</p>
                    <p class="test3">SansSerif：你好世界！Hello World! 123456</p>
                    <p class="test4">Serif：你好世界！Hello World! 123456</p>
                </body>
                </html>
                """;

        // 构建baseUrl
        String baseUrl = request.getScheme() + "://" +
                        request.getServerName() + ":" +
                        request.getServerPort() +
                        request.getContextPath();

        byte[] pdfBytes = PdfUtil.htmlToPdfWithBaseUrl(html, baseUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "simple-chinese-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/debug-fonts")
    public ResponseEntity<String> debugFonts() {
        // 输出字体调试信息到日志
        FontDebugger.debugFontInfo();

        return ResponseEntity.ok("字体调试信息已输出到日志，请查看应用日志");
    }

    @GetMapping("/font-debug-pdf")
    public ResponseEntity<byte[]> fontDebugPdf(HttpServletRequest request) {
        // 生成字体调试HTML
        String html = FontDebugger.generateFontTestHtml();

        // 构建baseUrl
        String baseUrl = request.getScheme() + "://" +
                        request.getServerName() + ":" +
                        request.getServerPort() +
                        request.getContextPath();

        byte[] pdfBytes = PdfUtil.htmlToPdfWithBaseUrl(html, baseUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "font-debug.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }
}

package app.admin;

import infra.report.code.CodeRenderOptions;
import infra.report.code.CodeUtil;
import infra.report.pdf.PdfRenderOptions;
import infra.report.pdf.PdfUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URI;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @GetMapping("/qrcode")
    public void generateQRCode(
            @RequestParam String data,
            HttpServletResponse response
    ) throws IOException {
        var config= CodeRenderOptions.defaultQRCode();
        BufferedImage logoImage = ImageIO.read(URI.create("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png").toURL());
        config.autoConfigLogo(logoImage);
        CodeUtil.generateQRCodeToResponse(data, config, response);
    }

    @GetMapping("/pdf")
    public ResponseEntity<byte[]> pdf() {
        String html = """
                <!DOCTYPE html>
                                   <html>
                                   <head>
                                       <meta charset="UTF-8">
                                       <title>基础示例</title>
                                       <style>
                                           body {
                                               font-family: SimHei, SimSun, Microsoft YaHei, SansSerif, sans-serif;
                                               font-size: 12pt;
                                           }
                                           h1 { color: #2c3e50; text-align: center; }
                                           table { width: 100%; border-collapse: collapse; }
                                           th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                           th { background-color: #f2f2f2; }
                                       </style>
                                   </head>
                                   <body>
                                       <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:30px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                   </body>
                                   </html>
            """;

        // 配置PDF选项，包括水印和页眉页脚
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .imagePath("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png")
                        .color("#cccccc")
                        .opacity(0.3f)
                        .rotation(-45f)
                        .fontSize(48f)
                        .position("center")
                        .repeatWatermark(true)           // 启用循环水印
                        .horizontalSpacing(200f)         // 水平间距
                        .verticalSpacing(150f)           // 垂直间距
                        .density(0.5f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerLeft("机密文档")
                        .headerLeftColor("#FF0000")
                        .headerCenter("Test Report")
                        .headerCenterColor("#0066CC")
                        .headerRight("DRAFT")
                        .headerRightColor("#FF6600")
                        .footerLeft("生成时间: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .showPageNumbers(true)
                        .fontSize(15f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        // headers.setContentDispositionFormData("attachment", "demo-report.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/font-cache-test")
    public ResponseEntity<byte[]> fontCacheTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>字体缓存测试</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .test-info {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                    </style>
                </head>
                <body>
                    <h1>字体缓存性能测试</h1>

                    <div class="test-info">
                        <h2>测试说明</h2>
                        <p>本测试用于验证字体缓存机制的性能优化效果：</p>
                        <ul>
                            <li>首次加载：系统会扫描并加载字体</li>
                            <li>后续请求：直接使用缓存，提高性能</li>
                            <li>自定义字体目录：只加载一次</li>
                            <li>系统字体：全局缓存，避免重复扫描</li>
                        </ul>
                    </div>

                    <div class="test-info">
                        <h2>中文字体测试</h2>
                        <p>这里是中文内容测试：你好世界！</p>
                        <p>测试各种中文字符：汉字、标点符号、数字123。</p>
                    </div>

                    <div class="test-info">
                        <h2>性能优化</h2>
                        <p>优化后的字体加载机制：</p>
                        <ol>
                            <li>使用缓存避免重复加载</li>
                            <li>按需加载自定义字体目录</li>
                            <li>系统字体只加载一次</li>
                            <li>线程安全的加载机制</li>
                        </ol>
                    </div>
                </body>
                </html>
                """;

        long startTime = System.currentTimeMillis();

        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("FONT TEST")
                        .color("#CCCCCC")
                        .opacity(0.2f)
                        .rotation(45f)
                        .fontSize(32f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("字体缓存测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("生成时间: " + (System.currentTimeMillis() - startTime) + "ms")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        long endTime = System.currentTimeMillis();
        log.info("字体缓存测试完成，耗时: {}ms", endTime - startTime);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "font-cache-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/resource-font-test")
    public ResponseEntity<byte[]> resourceFontTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>资源字体测试</title>
                    <style>
                        body {
                            font-family: 'simhei', sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .font-test {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .chinese-text {
                            font-size: 16px;
                            margin: 10px 0;
                        }
                    </style>
                </head>
                <body>
                    <h1>资源字体测试页面</h1>

                    <div class="font-test">
                        <h2>中文字体测试</h2>
                        <div class="chinese-text">这是使用资源字体的中文测试文本。</div>
                        <div class="chinese-text">测试各种中文字符：你好世界！</div>
                        <div class="chinese-text">数字和符号：123456789，。；：""''</div>
                        <div class="chinese-text">繁体中文：繁體中文測試內容</div>
                    </div>

                    <div class="font-test">
                        <h2>字体加载说明</h2>
                        <ul>
                            <li>优先使用resources/fonts/目录下的字体文件</li>
                            <li>支持SimHei.ttf、SimSun.ttf等常见中文字体</li>
                            <li>如果配置了外部字体目录，会同时加载</li>
                            <li>适用于JAR包和Docker部署</li>
                        </ul>
                    </div>

                    <div class="font-test">
                        <h2>部署优势</h2>
                        <ol>
                            <li>字体文件打包在JAR中，无需外部依赖</li>
                            <li>Docker镜像包含字体，开箱即用</li>
                            <li>支持外部字体目录扩展</li>
                            <li>自动字体回退机制</li>
                        </ol>
                    </div>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")  // 使用资源字体
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("资源字体")
                        .color("#CCCCCC")
                        .opacity(0.2f)
                        .rotation(45f)
                        .fontSize(32f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("资源字体测试报告")
                        .headerCenterColor("#0066CC")
                        .footerLeft("使用内置字体")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "resource-font-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/optimized-font-test")
    public ResponseEntity<byte[]> optimizedFontTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>优化字体测试</title>
                    <style>
                        body {
                            font-family: 'simhei', sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .test-section {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                    </style>
                </head>
                <body>
                    <h1>优化后的字体处理测试</h1>

                    <div class="test-section">
                        <h2>字体处理优化</h2>
                        <ul>
                            <li>统一的字体缓存管理</li>
                            <li>自动扫描资源字体目录</li>
                            <li>简洁优雅的字体加载逻辑</li>
                            <li>高性能的字体初始化</li>
                        </ul>
                    </div>

                    <div class="test-section">
                        <h2>中文字体测试</h2>
                        <p>这是中文内容测试：你好世界！</p>
                        <p>繁体中文：繁體中文測試內容</p>
                        <p>特殊符号：①②③④⑤⑥⑦⑧⑨⑩</p>
                    </div>

                    <div class="test-section">
                        <h2>性能优化特性</h2>
                        <ol>
                            <li>字体只初始化一次，全局缓存</li>
                            <li>智能扫描，避免硬编码字体列表</li>
                            <li>统一的字体管理，支持HTML和页眉页脚</li>
                            <li>优雅的错误处理和降级机制</li>
                        </ol>
                    </div>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("优化字体")
                        .color("#DDDDDD")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(36f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("字体优化测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("统一字体管理")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "optimized-font-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }
}

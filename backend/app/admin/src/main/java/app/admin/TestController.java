package app.admin;

import infra.report.code.CodeRenderOptions;
import infra.report.code.CodeUtil;
import infra.report.pdf.PdfRenderOptions;
import infra.report.pdf.PdfUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {

    @GetMapping("/qrcode")
    public void generateQRCode(
            @RequestParam String data,
            HttpServletResponse response
    ) throws IOException {
        var config= CodeRenderOptions.defaultQRCode();
        BufferedImage logoImage = ImageIO.read(URI.create("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png").toURL());
        config.autoConfigLogo(logoImage);
        CodeUtil.generateQRCodeToResponse(data, config, response);
    }

    @GetMapping("/pdf")
    public ResponseEntity<byte[]> pdf() {
        String html = """
                <!DOCTYPE html>
                                   <html>
                                   <head>
                                       <meta charset="UTF-8">
                                       <title>基础示例</title>
                                       <style>
                                           body {
                                               font-family: simhei, SimHei, SimSun, Microsoft YaHei, SansSerif, sans-serif;
                                               font-size: 12pt;
                                           }
                                           h1 { color: #2c3e50; text-align: center; }
                                           table { width: 100%; border-collapse: collapse; }
                                           th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                           th { background-color: #f2f2f2; }
                                       </style>
                                   </head>
                                   <body>
                                       <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:30px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                        <h1>HTML转PDF基础示例</h1>
                                       <p>这是一个包含中文内容的PDF文档示例。</p>
                
                                       <h2>功能特性</h2>
                                       <ul>
                                           <li>支持中文字体</li>
                                           <li>支持现代CSS样式</li>
                                           <li>支持表格和列表</li>
                                           <li>支持图片和外部资源</li>
                                       </ul>
                
                                       <h2>数据表格</h2>
                                       <img src="/test/qrcode?data=ruson" width="100" style="border:1px solid red;" />
                                       <table>
                                           <tr>
                                               <th>姓名</th>
                                               <th>部门</th>
                                               <th>职位</th>
                                               <th>入职时间</th>
                                           </tr>
                                           <tr>
                                               <td>张三</td>
                                               <td>技术部</td>
                                               <td>高级工程师</td>
                                               <td>2023-01-15</td>
                                           </tr>
                                           <tr>
                                               <td>李四</td>
                                               <td>产品部</td>
                                               <td>产品经理</td>
                                               <td>2023-03-20</td>
                                           </tr>
                                           <tr>
                                               <td>王五</td>
                                               <td>设计部</td>
                                               <td>UI设计师</td>
                                               <td>2023-05-10</td>
                                           </tr>
                                       </table>
                                   </body>
                                   </html>
            """;

        // 配置PDF选项，包括水印和页眉页脚
        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .imagePath("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png")
                        .color("#cccccc")
                        .opacity(0.3f)
                        .rotation(-45f)
                        .fontSize(48f)
                        .position("center")
                        .repeatWatermark(true)           // 启用循环水印
                        .horizontalSpacing(200f)         // 水平间距
                        .verticalSpacing(150f)           // 垂直间距
                        .density(0.5f)
                        .build())
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerLeft("机密文档")
                        .headerLeftColor("#FF0000")
                        .headerCenter("Test Report")
                        .headerCenterColor("#0066CC")
                        .headerRight("DRAFT")
                        .headerRightColor("#FF6600")
                        .footerLeft("生成时间: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .showPageNumbers(true)
                        .fontSize(15f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        // headers.setContentDispositionFormData("attachment", "demo-report.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/openhtmltopdf-test")
    public ResponseEntity<byte[]> openHtmlToPdfTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>OpenHtmlToPdf测试</title>
                    <style>
                        body {
                            font-family: 'simhei', sans-serif;
                            padding: 40px;
                            line-height: 1.6;
                        }
                        h1 { color: #2c3e50; }
                        .test-section {
                            background-color: #f8f9fa;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                            border: 1px solid #dee2e6;
                        }
                        .highlight {
                            background-color: #fff3cd;
                            padding: 10px;
                            border-left: 4px solid #ffc107;
                        }
                    </style>
                </head>
                <body>
                    <h1>OpenHtmlToPdf 重构测试</h1>

                    <div class="highlight">
                        <strong>新特性：</strong>基于OpenHtmlToPdf的现代化PDF生成方案
                    </div>

                    <div class="test-section">
                        <h2>中文字体测试</h2>
                        <p>简体中文：你好世界！这是基于OpenHtmlToPdf的中文测试。</p>
                        <p>繁体中文：繁體中文測試內容顯示效果。</p>
                        <p>中文标点：，。；：""''！？</p>
                        <p>中文数字：一二三四五六七八九十</p>
                    </div>

                    <div class="test-section">
                        <h2>英文内容测试</h2>
                        <p>English: The quick brown fox jumps over the lazy dog.</p>
                        <p>Numbers: 1234567890</p>
                        <p>Symbols: !@#$%^&*()</p>
                    </div>

                    <div class="test-section">
                        <h2>混合内容测试</h2>
                        <p>Mixed: English 中文 123 符号!@# 测试内容</p>
                        <p>Unicode: äöüÄÖÜß€£¥$ 中文字符</p>
                    </div>

                    <div class="test-section">
                        <h2>技术优势</h2>
                        <ul>
                            <li>基于现代化的OpenHtmlToPdf库</li>
                            <li>更好的CSS支持和HTML5兼容性</li>
                            <li>优化的中文字体处理</li>
                            <li>完整的页眉页脚和水印支持</li>
                            <li>更稳定的PDF生成性能</li>
                        </ul>
                    </div>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .marginTop(50f)
                .marginBottom(50f)
                .marginLeft(40f)
                .marginRight(40f)
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("OpenHtmlToPdf 重构测试")
                        .headerCenterColor("#0066CC")
                        .footerLeft("基于OpenHtmlToPdf")
                        .footerLeftColor("#666666")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#333333")
                        .fontSize(10f)
                        .build())
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("重构成功")
                        .color("#DDDDDD")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(36f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "openhtmltopdf-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/simple-openhtmltopdf-test")
    public ResponseEntity<byte[]> simpleOpenHtmlToPdfTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>简单测试</title>
                </head>
                <body>
                    <h1>OpenHtmlToPdf 简单测试</h1>
                    <p>这是一个简单的测试页面。</p>
                    <p>English: Hello World!</p>
                    <p>中文: 你好世界！</p>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "simple-openhtmltopdf-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/latest-openhtmltopdf-test")
    public ResponseEntity<byte[]> latestOpenHtmlToPdfTest() {
        String html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>最新OpenHtmlToPdf测试</title>
                    <style>
                        body {
                            font-family: 'simhei', 'Microsoft YaHei', sans-serif;
                            padding: 40px;
                            line-height: 1.8;
                            color: #333;
                        }
                        h1 {
                            color: #2c3e50;
                            border-bottom: 3px solid #3498db;
                            padding-bottom: 10px;
                        }
                        .feature-box {
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                        }
                        .test-section {
                            background-color: #f8f9fa;
                            padding: 25px;
                            border-radius: 8px;
                            margin: 20px 0;
                            border-left: 5px solid #28a745;
                        }
                        .highlight {
                            background-color: #fff3cd;
                            padding: 15px;
                            border-left: 4px solid #ffc107;
                            margin: 15px 0;
                        }
                        .code-block {
                            background-color: #f4f4f4;
                            padding: 15px;
                            border-radius: 5px;
                            font-family: 'Courier New', monospace;
                            border: 1px solid #ddd;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 12px;
                            text-align: left;
                        }
                        th {
                            background-color: #f2f2f2;
                            font-weight: bold;
                        }
                    </style>
                </head>
                <body>
                    <h1>🚀 最新OpenHtmlToPdf库测试</h1>

                    <div class="feature-box">
                        <h2>✨ 新版本特性</h2>
                        <ul>
                            <li>更好的HTML5和CSS3支持</li>
                            <li>改进的字体处理机制</li>
                            <li>增强的性能和稳定性</li>
                            <li>更完善的日志系统</li>
                        </ul>
                    </div>

                    <div class="test-section">
                        <h2>🔤 中文字体测试</h2>
                        <p>简体中文：你好世界！这是基于最新OpenHtmlToPdf的中文测试。</p>
                        <p>繁体中文：繁體中文測試內容顯示效果非常好。</p>
                        <p>中文标点：，。；：""''！？【】（）</p>
                        <p>中文数字：一二三四五六七八九十百千万</p>
                    </div>

                    <div class="test-section">
                        <h2>🌍 多语言测试</h2>
                        <p>English: The quick brown fox jumps over the lazy dog.</p>
                        <p>Français: Le renard brun et rapide saute par-dessus le chien paresseux.</p>
                        <p>Deutsch: Der schnelle braune Fuchs springt über den faulen Hund.</p>
                        <p>日本語: 素早い茶色の狐が怠惰な犬を飛び越える。</p>
                        <p>한국어: 빠른 갈색 여우가 게으른 개를 뛰어넘는다.</p>
                    </div>

                    <div class="highlight">
                        <strong>💡 提示：</strong>这个PDF是使用最新版本的OpenHtmlToPdf库生成的，
                        支持现代CSS特性如渐变、阴影、圆角等。
                    </div>

                    <div class="test-section">
                        <h2>📊 表格测试</h2>
                        <table>
                            <thead>
                                <tr>
                                    <th>功能</th>
                                    <th>状态</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>中文字体</td>
                                    <td>✅ 支持</td>
                                    <td>完美支持中文显示</td>
                                </tr>
                                <tr>
                                    <td>CSS3特性</td>
                                    <td>✅ 支持</td>
                                    <td>渐变、阴影、圆角等</td>
                                </tr>
                                <tr>
                                    <td>页眉页脚</td>
                                    <td>✅ 支持</td>
                                    <td>自定义页眉页脚</td>
                                </tr>
                                <tr>
                                    <td>水印功能</td>
                                    <td>✅ 支持</td>
                                    <td>透明度和旋转</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="code-block">
                        <strong>代码示例：</strong><br>
                        PdfRenderOptions options = PdfRenderOptions.builder()<br>
                        &nbsp;&nbsp;.defaultFontFamily("simhei")<br>
                        &nbsp;&nbsp;.pageSize(PageSize.A4)<br>
                        &nbsp;&nbsp;.build();<br>
                        <br>
                        byte[] pdf = PdfUtil.htmlToPdf(html, options);
                    </div>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .marginTop(60f)
                .marginBottom(60f)
                .marginLeft(50f)
                .marginRight(50f)
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("最新OpenHtmlToPdf库测试")
                        .headerCenterColor("#2c3e50")
                        .footerLeft("版本: 1.0.10")
                        .footerLeftColor("#7f8c8d")
                        .footerRight("第 {page} 页，共 {total} 页")
                        .footerRightColor("#34495e")
                        .fontSize(11f)
                        .build())
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("最新版本")
                        .color("#ecf0f1")
                        .opacity(0.25f)
                        .rotation(45f)
                        .fontSize(40f)
                        .build())
                .build();

        byte[] pdfBytes = PdfUtil.htmlToPdf(html, options);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "latest-openhtmltopdf-test.pdf");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pdfBytes);
    }

    @GetMapping("/performance-test")
    public ResponseEntity<String> performanceTest() {
        StringBuilder result = new StringBuilder();
        result.append("=== OpenHtmlToPdf 性能测试 ===\n\n");

        String simpleHtml = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>性能测试</title>
                    <style>
                        body { font-family: 'simhei', sans-serif; padding: 20px; }
                        h1 { color: #2c3e50; }
                        p { line-height: 1.6; }
                    </style>
                </head>
                <body>
                    <h1>性能测试页面</h1>
                    <p>这是一个用于测试PDF生成性能的简单页面。</p>
                    <p>中文内容：你好世界！这是中文测试内容。</p>
                    <p>English content: Hello World! This is English test content.</p>
                </body>
                </html>
                """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .defaultFontFamily("simhei")
                .build();

        // 预热
        try {
            PdfUtil.htmlToPdf(simpleHtml, options);
            result.append("✅ 预热完成\n\n");
        } catch (Exception e) {
            result.append("❌ 预热失败: ").append(e.getMessage()).append("\n\n");
            return ResponseEntity.ok()
                    .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
                    .body(result.toString());
        }

        // 性能测试
        int testCount = 5;
        long totalTime = 0;
        int successCount = 0;

        for (int i = 1; i <= testCount; i++) {
            try {
                long startTime = System.currentTimeMillis();
                byte[] pdf = PdfUtil.htmlToPdf(simpleHtml, options);
                long endTime = System.currentTimeMillis();

                long duration = endTime - startTime;
                totalTime += duration;
                successCount++;

                result.append(String.format("测试 %d: %d ms, PDF大小: %d bytes\n",
                    i, duration, pdf.length));

            } catch (Exception e) {
                result.append(String.format("测试 %d: 失败 - %s\n", i, e.getMessage()));
            }
        }

        result.append("\n=== 性能统计 ===\n");
        result.append(String.format("成功次数: %d/%d\n", successCount, testCount));
        if (successCount > 0) {
            result.append(String.format("平均耗时: %d ms\n", totalTime / successCount));
            result.append(String.format("总耗时: %d ms\n", totalTime));
        }

        return ResponseEntity.ok()
                .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
                .body(result.toString());
    }

    @GetMapping("/basic-test")
    public ResponseEntity<String> basicTest() {
        try {
            String html = "<html><body><h1>Basic Test</h1><p>Hello World!</p></body></html>";

            // 创建基本选项
            infra.report.pdf.PdfRenderOptions options = infra.report.pdf.PdfRenderOptions.builder()
                    .defaultFontFamily("Arial")
                    .build();

            byte[] pdf = infra.report.pdf.PdfUtil.htmlToPdf(html, options);

            return ResponseEntity.ok()
                    .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
                    .body("✅ 基础测试成功！PDF大小: " + pdf.length + " 字节");

        } catch (Exception e) {
            return ResponseEntity.ok()
                    .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
                    .body("❌ 基础测试失败: " + e.getMessage());
        }
    }
}

version: '3.8'

services:
  pdf-service:
    build:
      context: ../../../
      dockerfile: backend/infra/infra-report/docker/Dockerfile
    ports:
      - "8080:8080"
    volumes:
      # 挂载字体目录
      - ./fonts:/app/fonts:ro
      # 可选：挂载配置文件
      - ./config:/app/config:ro
    environment:
      # 字体路径配置
      - FONT_PATH=/app/fonts
      # JVM配置
      - JAVA_OPTS=-Xmx1g -Xms512m
      # 应用配置
      - SPRING_PROFILES_ACTIVE=docker
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：字体初始化容器
  font-init:
    image: alpine:latest
    volumes:
      - ./fonts:/fonts
    command: |
      sh -c "
        echo '正在准备字体文件...'
        # 这里可以添加字体下载逻辑
        # wget -O /fonts/SimHei.ttf 'https://example.com/fonts/SimHei.ttf'
        # wget -O /fonts/SimSun.ttf 'https://example.com/fonts/SimSun.ttf'
        echo '字体准备完成'
      "
    profiles:
      - init

volumes:
  fonts:
    driver: local

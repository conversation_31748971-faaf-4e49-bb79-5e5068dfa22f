# 多阶段构建的Dockerfile示例
FROM openjdk:24-jdk-slim as builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml .
COPY infra/infra-report/pom.xml infra/infra-report/

# 复制源代码
COPY infra/infra-report/src infra/infra-report/src

# 构建应用
RUN ./mvnw clean package -DskipTests

# 运行时镜像
FROM openjdk:24-jre-slim

# 安装字体相关工具
RUN apt-get update && \
    apt-get install -y fontconfig && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 创建字体目录
RUN mkdir -p /app/fonts && \
    chown -R appuser:appuser /app

# 设置工作目录
WORKDIR /app

# 复制应用JAR
COPY --from=builder /app/infra/infra-report/target/*.jar app.jar

# 切换到应用用户
USER appuser

# 设置环境变量
ENV FONT_PATH=/app/fonts
ENV JAVA_OPTS="-Xmx512m -Xms256m"

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

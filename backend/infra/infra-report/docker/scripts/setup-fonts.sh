#!/bin/bash

# 字体设置脚本
# 用于在Docker容器启动前准备字体文件

set -e

FONT_DIR="${FONT_PATH:-/app/fonts}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "开始设置字体..."
echo "字体目录: $FONT_DIR"

# 创建字体目录
mkdir -p "$FONT_DIR"

# 检查字体目录是否可写
if [ ! -w "$FONT_DIR" ]; then
    echo "错误: 字体目录不可写: $FONT_DIR"
    exit 1
fi

# 字体下载函数
download_font() {
    local font_name="$1"
    local font_url="$2"
    local font_file="$FONT_DIR/$font_name"
    
    if [ -f "$font_file" ]; then
        echo "字体已存在: $font_name"
        return 0
    fi
    
    echo "下载字体: $font_name"
    if command -v wget >/dev/null 2>&1; then
        wget -q -O "$font_file" "$font_url"
    elif command -v curl >/dev/null 2>&1; then
        curl -s -o "$font_file" "$font_url"
    else
        echo "警告: 未找到wget或curl，无法下载字体"
        return 1
    fi
    
    if [ -f "$font_file" ]; then
        echo "字体下载成功: $font_name"
    else
        echo "字体下载失败: $font_name"
        return 1
    fi
}

# 从系统复制字体函数
copy_system_font() {
    local font_name="$1"
    local system_paths=(
        "/usr/share/fonts"
        "/System/Library/Fonts"
        "/Library/Fonts"
        "/Windows/Fonts"
        "$HOME/.fonts"
    )
    
    local font_file="$FONT_DIR/$font_name"
    if [ -f "$font_file" ]; then
        echo "字体已存在: $font_name"
        return 0
    fi
    
    for path in "${system_paths[@]}"; do
        if [ -d "$path" ]; then
            local found_font=$(find "$path" -name "$font_name" -type f 2>/dev/null | head -1)
            if [ -n "$found_font" ]; then
                echo "从系统复制字体: $font_name"
                cp "$found_font" "$font_file"
                return 0
            fi
        fi
    done
    
    echo "系统中未找到字体: $font_name"
    return 1
}

# 设置常用中文字体
setup_chinese_fonts() {
    echo "设置中文字体..."
    
    # 定义字体映射（字体文件名 -> 下载URL）
    declare -A fonts=(
        ["SimHei.ttf"]="https://github.com/StellarCN/scp_zh/raw/master/fonts/SimHei.ttf"
        ["SimSun.ttf"]="https://github.com/StellarCN/scp_zh/raw/master/fonts/SimSun.ttf"
        ["NotoSansCJK-Regular.ttc"]="https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTC/NotoSansCJK-Regular.ttc"
        ["SourceHanSans-Regular.ttc"]="https://github.com/adobe-fonts/source-han-sans/raw/release/OTC/SourceHanSans-Regular.ttc"
    )
    
    for font_name in "${!fonts[@]}"; do
        font_url="${fonts[$font_name]}"
        
        # 首先尝试从系统复制
        if ! copy_system_font "$font_name"; then
            # 如果系统中没有，尝试下载
            if [ -n "$font_url" ]; then
                download_font "$font_name" "$font_url" || true
            fi
        fi
    done
}

# 验证字体文件
validate_fonts() {
    echo "验证字体文件..."
    
    local font_count=0
    for font_file in "$FONT_DIR"/*.{ttf,ttc,otf}; do
        if [ -f "$font_file" ]; then
            local file_size=$(stat -f%z "$font_file" 2>/dev/null || stat -c%s "$font_file" 2>/dev/null || echo "0")
            if [ "$file_size" -gt 1000 ]; then
                echo "✓ $(basename "$font_file") (${file_size} bytes)"
                ((font_count++))
            else
                echo "✗ $(basename "$font_file") (文件太小，可能损坏)"
                rm -f "$font_file"
            fi
        fi
    done
    
    echo "共找到 $font_count 个有效字体文件"
    
    if [ $font_count -eq 0 ]; then
        echo "警告: 未找到任何字体文件，PDF生成可能无法正确显示中文"
    fi
}

# 生成字体信息文件
generate_font_info() {
    local info_file="$FONT_DIR/fonts.info"
    echo "生成字体信息文件: $info_file"
    
    {
        echo "# 字体信息文件"
        echo "# 生成时间: $(date)"
        echo "# 字体目录: $FONT_DIR"
        echo ""
        
        for font_file in "$FONT_DIR"/*.{ttf,ttc,otf}; do
            if [ -f "$font_file" ]; then
                local font_name=$(basename "$font_file")
                local file_size=$(stat -f%z "$font_file" 2>/dev/null || stat -c%s "$font_file" 2>/dev/null || echo "unknown")
                echo "$font_name|$file_size"
            fi
        done
    } > "$info_file"
}

# 主函数
main() {
    echo "========================================="
    echo "PDF服务字体设置脚本"
    echo "========================================="
    
    setup_chinese_fonts
    validate_fonts
    generate_font_info
    
    echo "========================================="
    echo "字体设置完成！"
    echo "字体目录: $FONT_DIR"
    echo "========================================="
}

# 如果脚本被直接执行
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi

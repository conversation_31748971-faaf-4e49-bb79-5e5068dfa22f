package infra.report.pdf;

import com.lowagie.text.FontFactory;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.util.Set;

/**
 * 字体调试工具
 * 用于诊断字体加载和显示问题
 */
@Slf4j
public class FontDebugger {

    /**
     * 调试字体信息
     */
    public static void debugFontInfo() {
        log.info("========== 字体调试信息 ==========");
        
        // 1. 检查系统可用字体
        debugSystemFonts();
        
        // 2. 检查FontFactory注册的字体
        debugFontFactoryFonts();
        
        // 3. 检查FontManager注册的字体
        debugFontManagerFonts();
        
        // 4. 测试中文字符支持
        debugChineseSupport();
        
        log.info("========== 字体调试完成 ==========");
    }

    /**
     * 调试系统字体
     */
    private static void debugSystemFonts() {
        log.info("--- 系统可用字体 ---");
        try {
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            String[] fontNames = ge.getAvailableFontFamilyNames();
            
            log.info("系统字体总数: {}", fontNames.length);
            
            // 查找中文字体
            int chineseFontCount = 0;
            for (String fontName : fontNames) {
                if (isChinese(fontName)) {
                    log.info("中文字体: {}", fontName);
                    chineseFontCount++;
                }
            }
            
            log.info("中文字体数量: {}", chineseFontCount);
            
            // 显示一些常见字体的可用性
            String[] commonFonts = {"SimHei", "SimSun", "Microsoft YaHei", "Arial", "Helvetica"};
            for (String font : commonFonts) {
                boolean available = java.util.Arrays.asList(fontNames).contains(font);
                log.info("字体 {} 可用: {}", font, available);
            }
            
        } catch (Exception e) {
            log.error("获取系统字体信息失败", e);
        }
    }

    /**
     * 调试FontFactory字体
     */
    private static void debugFontFactoryFonts() {
        log.info("--- FontFactory 注册字体 ---");
        try {
            Set<String> registeredFonts = FontFactory.getRegisteredFonts();
            log.info("FontFactory注册字体数量: {}", registeredFonts.size());
            
            for (String font : registeredFonts) {
                log.info("FontFactory字体: {}", font);
            }
            
        } catch (Exception e) {
            log.error("获取FontFactory字体信息失败", e);
        }
    }

    /**
     * 调试FontManager字体
     */
    private static void debugFontManagerFonts() {
        log.info("--- FontManager 注册字体 ---");
        try {
            FontManager fontManager = FontManager.getInstance();
            Set<String> fonts = fontManager.getRegisteredFonts();
            
            log.info("FontManager注册字体数量: {}", fonts.size());
            
            for (String font : fonts) {
                String path = fontManager.getFontPath(font);
                log.info("FontManager字体: {} -> {}", font, path);
            }
            
        } catch (Exception e) {
            log.error("获取FontManager字体信息失败", e);
        }
    }

    /**
     * 测试中文字符支持
     */
    private static void debugChineseSupport() {
        log.info("--- 中文字符支持测试 ---");
        
        String testText = "你好世界";
        String[] testFonts = {"SimHei", "SimSun", "Microsoft YaHei", "SansSerif", "Serif"};
        
        for (String fontName : testFonts) {
            try {
                Font font = new Font(fontName, Font.PLAIN, 12);
                boolean canDisplay = font.canDisplayUpTo(testText) == -1;
                log.info("字体 {} 支持中文 '{}': {}", fontName, testText, canDisplay);
            } catch (Exception e) {
                log.warn("测试字体 {} 中文支持失败: {}", fontName, e.getMessage());
            }
        }
    }

    /**
     * 判断是否为中文字体
     */
    private static boolean isChinese(String fontName) {
        String lowerName = fontName.toLowerCase();
        return lowerName.contains("sim") || 
               lowerName.contains("song") || 
               lowerName.contains("hei") || 
               lowerName.contains("kai") || 
               lowerName.contains("fang") || 
               lowerName.contains("yahei") || 
               lowerName.contains("chinese") || 
               lowerName.contains("cjk") || 
               lowerName.contains("noto") ||
               lowerName.contains("source han");
    }

    /**
     * 生成字体测试HTML
     */
    public static String generateFontTestHtml() {
        StringBuilder html = new StringBuilder();
        
        html.append("<!DOCTYPE html>\n");
        html.append("<html>\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <title>字体调试测试</title>\n");
        html.append("    <style>\n");
        html.append("        body { padding: 20px; line-height: 1.6; }\n");
        html.append("        .font-test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }\n");
        
        // 为每个注册的字体生成CSS类
        FontManager fontManager = FontManager.getInstance();
        Set<String> fonts = fontManager.getRegisteredFonts();
        
        for (String font : fonts) {
            html.append("        .font-").append(font.replaceAll("[^a-zA-Z0-9]", ""))
                .append(" { font-family: ").append(font).append("; }\n");
        }
        
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <h1>字体调试测试页面</h1>\n");
        html.append("    <p>测试文本：你好世界！Hello World! 1234567890</p>\n");
        
        // 为每个字体生成测试段落
        for (String font : fonts) {
            String cssClass = "font-" + font.replaceAll("[^a-zA-Z0-9]", "");
            html.append("    <div class=\"font-test ").append(cssClass).append("\">\n");
            html.append("        <strong>").append(font).append(":</strong> ");
            html.append("你好世界！Hello World! 1234567890\n");
            html.append("    </div>\n");
        }
        
        html.append("</body>\n");
        html.append("</html>\n");
        
        return html.toString();
    }

    /**
     * 创建简单的中文测试HTML
     */
    public static String createSimpleChineseTestHtml() {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>简单中文测试</title>
                <style>
                    body { 
                        font-family: SimHei, SimSun, SansSerif, sans-serif;
                        font-size: 14pt;
                        padding: 20px;
                    }
                </style>
            </head>
            <body>
                <h1>中文测试</h1>
                <p>这是一个简单的中文测试页面。</p>
                <p>如果您能看到这些中文字符，说明字体加载成功。</p>
                <p>测试内容：你好世界！欢迎使用PDF生成服务。</p>
            </body>
            </html>
            """;
    }
}

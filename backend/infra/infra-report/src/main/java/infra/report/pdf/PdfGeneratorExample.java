package infra.report.pdf;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * PDF生成器使用示例
 */
public class PdfGeneratorExample {

    public static void main(String[] args) {
        try {
            // 示例1：基础HTML转PDF
            basicExample();
            
            // 示例2：带水印的PDF
            watermarkExample();
            
            // 示例3：带页眉页脚的PDF
            headerFooterExample();
            
            System.out.println("所有示例PDF生成完成！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 基础HTML转PDF示例
     */
    private static void basicExample() throws IOException {
        String html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>基础示例</title>
                <style>
                    body { font-family: 'SimHei', sans-serif; }
                    h1 { color: #2c3e50; text-align: center; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                </style>
            </head>
            <body>
                <h1>HTML转PDF基础示例</h1>
                <p>这是一个包含中文内容的PDF文档示例。</p>
                
                <h2>功能特性</h2>
                <ul>
                    <li>支持中文字体</li>
                    <li>支持现代CSS样式</li>
                    <li>支持表格和列表</li>
                    <li>支持图片和外部资源</li>
                </ul>
                
                <h2>数据表格</h2>
                <table>
                    <tr>
                        <th>姓名</th>
                        <th>部门</th>
                        <th>职位</th>
                        <th>入职时间</th>
                    </tr>
                    <tr>
                        <td>张三</td>
                        <td>技术部</td>
                        <td>高级工程师</td>
                        <td>2023-01-15</td>
                    </tr>
                    <tr>
                        <td>李四</td>
                        <td>产品部</td>
                        <td>产品经理</td>
                        <td>2023-03-20</td>
                    </tr>
                    <tr>
                        <td>王五</td>
                        <td>设计部</td>
                        <td>UI设计师</td>
                        <td>2023-05-10</td>
                    </tr>
                </table>
            </body>
            </html>
            """;

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html);
        saveToFile(pdfBytes, "basic-example.pdf");
        System.out.println("基础示例PDF生成成功: basic-example.pdf");
    }

    /**
     * 带水印的PDF示例
     */
    private static void watermarkExample() throws IOException {
        String html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>水印示例</title>
            </head>
            <body>
                <h1>带水印的PDF文档</h1>
                <p>这个文档演示了如何添加文本水印。</p>
                
                <div style="height: 300px; padding: 20px; border: 1px solid #ccc;">
                    <h2>重要内容</h2>
                    <p>这里是一些重要的机密内容，需要添加水印保护。</p>
                    <p>水印会显示在页面的背景中，不会影响正常的内容阅读。</p>
                </div>
                
                <p>更多内容...</p>
            </body>
            </html>
            """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("机密文档")
                        .color("#FF6B6B")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(48f)
                        .position("center")
                        .build())
                .build();

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, options);
        saveToFile(pdfBytes, "watermark-example.pdf");
        System.out.println("水印示例PDF生成成功: watermark-example.pdf");
    }

    /**
     * 带页眉页脚的PDF示例
     */
    private static void headerFooterExample() throws IOException {
        String html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>页眉页脚示例</title>
            </head>
            <body>
                <h1>带页眉页脚的PDF文档</h1>
                <p>这个文档演示了如何添加页眉和页脚。</p>
                
                <div style="height: 600px;">
                    <h2>第一页内容</h2>
                    <p>这是第一页的内容...</p>
                </div>
                
                <div style="page-break-before: always; height: 600px;">
                    <h2>第二页内容</h2>
                    <p>这是第二页的内容...</p>
                </div>
                
                <div style="page-break-before: always;">
                    <h2>第三页内容</h2>
                    <p>这是第三页的内容...</p>
                </div>
            </body>
            </html>
            """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("公司内部文档")
                        .headerRight("2024年度报告")
                        .footerLeft("机密")
                        .footerRight("版权所有")
                        .showPageNumbers(true)
                        .pageNumberFormat("第 {page} 页，共 {total} 页")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, options);
        saveToFile(pdfBytes, "header-footer-example.pdf");
        System.out.println("页眉页脚示例PDF生成成功: header-footer-example.pdf");
    }

    /**
     * 保存PDF到文件
     */
    private static void saveToFile(byte[] pdfBytes, String filename) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filename)) {
            fos.write(pdfBytes);
        }
    }
}

package infra.report.pdf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * PDF服务初始化器
 * 在应用启动时初始化字体管理器，避免首次使用时的延迟
 */
@Component
@Slf4j
public class PdfServiceInitializer implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化PDF服务...");
        
        try {
            // 初始化字体管理器
            FontManager fontManager = FontManager.getInstance();
            fontManager.initialize();
            
            int fontCount = fontManager.getRegisteredFonts().size();
            log.info("PDF服务初始化完成，已注册 {} 个字体", fontCount);
            
            // 输出字体状态信息
            if (fontCount == 0) {
                log.warn("未注册任何字体，PDF生成可能无法正确显示中文");
                log.info("请确保字体文件存在于以下位置之一：");
                log.info("1. 环境变量 FONT_PATH 指定的目录");
                log.info("2. 系统属性 font.path 指定的目录");
                log.info("3. 默认目录 /app/fonts");
                log.info("4. classpath 中的 fonts 目录");
            } else {
                log.info("字体注册成功，PDF服务已就绪");
            }
            
        } catch (Exception e) {
            log.error("PDF服务初始化失败", e);
            // 不抛出异常，允许应用继续启动
        }
    }
}

package infra.report.pdf;

import com.openhtmltopdf.extend.FSSupplier;
import com.openhtmltopdf.extend.FontResolver;
import com.openhtmltopdf.outputdevice.helper.BaseRenderer;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.UUID;

/**
 * Modern PDF Generator using OpenHtmlToPdf.
 * This class provides robust and feature-rich PDF generation from HTML content.
 */
@Slf4j
public class PdfGenerator {

    /**
     * Generates a PDF from an HTML string to a given OutputStream.
     *
     * @param htmlContent  The HTML content to convert.
     * @param outputStream The stream to write the PDF to.
     * @param options      The rendering options for the PDF.
     */
    public static void generatePdf(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        if (Str.isEmpty(htmlContent)) {
            throw new PdfException("HTML content cannot be empty.");
        }
        if (outputStream == null) {
            throw new PdfException("OutputStream cannot be null.");
        }
        if (options == null) {
            options = new PdfRenderOptions();
        }

        try {
            // 1. Prepare the PDF renderer builder
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFastMode();
            builder.usePdfAConformance(PdfRendererBuilder.PdfAConformance.PDFA_2_A);

            // 2. Configure fonts
            configureFonts(builder);

            // 3. Preprocess HTML to inject styles for options (watermark, header/footer, etc.)
            String processedHtml = preprocessHtml(htmlContent, options);

            // 4. Set the base URL for resolving relative resources (images, CSS)
            String baseUrl = Str.isEmpty(options.getBaseUrl()) ? getDefaultBaseUrl() : options.getBaseUrl();
            
            // Use Jsoup to parse and convert to W3C DOM, which is required by the renderer
            Document doc = Jsoup.parse(processedHtml, baseUrl);
            org.w3c.dom.Document w3cDoc = new W3CDom().fromJsoup(doc);

            builder.withW3cDocument(w3cDoc, baseUrl);
            
            // 5. Build and run the renderer
            builder.toStream(outputStream);
            builder.run();

            log.info("PDF generation successful.");

        } catch (Exception e) {
            log.error("Failed to generate PDF", e);
            throw new PdfException("PDF generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * Configures the font resolver to automatically discover and use fonts
     * from the 'resources/fonts' directory.
     *
     * @param builder The PdfRendererBuilder to configure.
     */
    private static void configureFonts(PdfRendererBuilder builder) {
        try {
            FontResolver fontResolver = builder.getFontResolver();
            
            // Scan the 'resources/fonts' directory
            URI fontsUri = PdfGenerator.class.getClassLoader().getResource("fonts").toURI();
            Path fontsPath = Paths.get(fontsUri);

            Files.walk(fontsPath)
                .filter(file -> file.toString().toLowerCase().endsWith(".ttf") ||
                               file.toString().toLowerCase().endsWith(".otf") ||
                               file.toString().toLowerCase().endsWith(".ttc"))
                .forEach(fontFile -> {
                    try {
                        // The family name is derived from the file name (e.g., "simhei.ttf" -> "simhei")
                        String familyName = fontFile.getFileName().toString().toLowerCase().split("\.")[0];
                        
                        // For TTC files, we must specify which font in the collection to use.
                        // We'll register each font in the collection under a unique name.
                        if (fontFile.toString().toLowerCase().endsWith(".ttc")) {
                            com.openhtmltopdf.pdfboxout.PdfBoxFontResolver.FontCollection fontCollection = 
                                new com.openhtmltopdf.pdfboxout.PdfBoxFontResolver.FontCollection(Files.newInputStream(fontFile));
                            for (int i = 0; i < fontCollection.getNrOfFonts(); i++) {
                                String collectionFamilyName = familyName + "-" + i;
                                final int fontIndex = i;
                                fontResolver.addFont(new FSSupplier<InputStream>() {
                                    @Override
                                    public InputStream supply() {
                                        try {
                                            return Files.newInputStream(fontFile);
                                        } catch (Exception e) {
                                            log.error("Failed to supply font stream for {}", fontFile, e);
                                            return null;
                                        }
                                    }
                                }, collectionFamilyName, null, null, false, fontIndex);
                                log.debug("Registered TTC font: {} as {}", fontFile, collectionFamilyName);
                            }
                        } else {
                            fontResolver.addFont(new FSSupplier<InputStream>() {
                                @Override
                                public InputStream supply() {
                                    try {
                                        return Files.newInputStream(fontFile);
                                    } catch (Exception e) {
                                        log.error("Failed to supply font stream for {}", fontFile, e);
                                        return null;
                                    }
                                }
                            }, familyName, null, null, false);
                            log.debug("Registered TTF/OTF font: {} as {}", fontFile, familyName);
                        }
                    } catch (Exception e) {
                        log.error("Failed to register font: {}", fontFile, e);
                    }
                });
            log.info("Font configuration complete. Registered fonts from 'resources/fonts'.");
        } catch (Exception e) {
            log.warn("Could not configure fonts from 'resources/fonts'. Using default fonts.", e);
        }
    }

    /**
     * Preprocesses the HTML to inject CSS rules for watermarks, headers, footers,
     * and other dynamic options.
     *
     * @param htmlContent The original HTML content.
     * @param options     The rendering options.
     * @return The processed HTML with injected styles.
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        Document doc = Jsoup.parse(htmlContent);
        Element head = doc.head();
        
        StringBuilder style = new StringBuilder();

        // --- Page Size and Margins ---
        style.append("@page {");
        if (options.getPageSize() != null) {
            style.append("size: ").append(options.getPageSize().name()).append(";");
        }
        style.append(String.format("margin: %spt %spt %spt %spt;", 
            options.getMarginTop(), options.getMarginRight(), options.getMarginBottom(), options.getMarginLeft()));
        style.append("}");

        // --- Default Font ---
        style.append("body { font-family: '").append(options.getDefaultFontFamily()).append("', sans-serif; }");

        // --- Header and Footer ---
        if (options.getHeaderFooterOptions() != null) {
            PdfRenderOptions.HeaderFooterOptions hf = options.getHeaderFooterOptions();
            style.append("@page {");
            appendHeaderFooterCss(style, "@top-left", hf.getHeaderLeft(), hf.getHeaderLeftColor(), hf.getFontSize());
            appendHeaderFooterCss(style, "@top-center", hf.getHeaderCenter(), hf.getHeaderCenterColor(), hf.getFontSize());
            appendHeaderFooterCss(style, "@top-right", hf.getHeaderRight(), hf.getHeaderRightColor(), hf.getFontSize());
            appendHeaderFooterCss(style, "@bottom-left", hf.getFooterLeft(), hf.getFooterLeftColor(), hf.getFontSize());
            appendHeaderFooterCss(style, "@bottom-center", hf.getFooterCenter(), hf.getFooterCenterColor(), hf.getFontSize());
            appendHeaderFooterCss(style, "@bottom-right", hf.getFooterRight(), hf.getFooterRightColor(), hf.getFontSize());
            style.append("}");
        }

        // --- Watermark ---
        if (options.getWatermarkOptions() != null) {
            PdfRenderOptions.WatermarkOptions wm = options.getWatermarkOptions();
            String watermarkId = "watermark-" + UUID.randomUUID().toString();
            
            style.append("body::before {");
            style.append("position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1000;");
            style.append("content: '';");
            
            if (Str.isNotEmpty(wm.getImagePath())) {
                try {
                    byte[] imageBytes = new URI(wm.getImagePath()).toURL().openStream().readAllBytes();
                    String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                    style.append("background-image: url('data:image/png;base64,").append(base64Image).append("');");
                    style.append("background-repeat: ").append(wm.isRepeatWatermark() ? "repeat" : "no-repeat").append(";");
                } catch (Exception e) {
                    log.error("Failed to load watermark image: {}", wm.getImagePath(), e);
                }
            } else if (Str.isNotEmpty(wm.getText())) {
                // This is a more complex scenario, often requiring SVG for rotated text watermarks.
                // For simplicity, we'll add a non-rotated text. For rotation, an SVG background is best.
                String svg = String.format(
                    "<svg xmlns='http://www.w3.org/2000/svg' version='1.1' width='400' height='400'>" +
                    "<text x='200' y='200' font-size='%f' fill='%s' fill-opacity='%f' transform='rotate(%f, 200, 200)' text-anchor='middle'>%s</text>" +
                    "</svg>",
                    wm.getFontSize(), wm.getColor(), wm.getOpacity(), wm.getRotation(), wm.getText()
                );
                String base64Svg = Base64.getEncoder().encodeToString(svg.getBytes(StandardCharsets.UTF_8));
                style.append("background-image: url('data:image/svg+xml;base64,").append(base64Svg).append("');");
                style.append("background-repeat: repeat;");
            }
            style.append(String.format("opacity: %f;", wm.getOpacity()));
            style.append("}");
        }

        head.append("<style>" + style.toString() + "</style>");
        return doc.outerHtml();
    }

    private static void appendHeaderFooterCss(StringBuilder style, String position, String content, String color, float fontSize) {
        if (Str.isNotEmpty(content)) {
            style.append(position).append(" {");
            style.append("content: '").append(content.replace("'", "\'")).append("';");
            style.append("font-family: 'simhei', sans-serif;"); // Use a default for headers/footers
            style.append("font-size: ").append(fontSize).append("pt;");
            if (Str.isNotEmpty(color)) {
                style.append("color: ").append(color).append(";");
            }
            style.append("}");
        }
    }

    /**
     * Gets the default base URL from the current servlet context, if available.
     *
     * @return The base URL as a string, or an empty string if not available.
     */
    private static String getDefaultBaseUrl() {
        try {
            var request = ServletUtil.getRequest();
            if (request != null) {
                return request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
            }
        } catch (Exception e) {
            log.debug("Cannot get base URL from servlet context. Will use local file resolution.");
        }
        // Fallback for non-servlet environments
        return Paths.get("").toUri().toString();
    }
}

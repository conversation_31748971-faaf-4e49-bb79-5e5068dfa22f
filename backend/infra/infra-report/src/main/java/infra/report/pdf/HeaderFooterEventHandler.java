package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 页眉页脚事件处理器
 */
@Slf4j
public class HeaderFooterEventHandler extends PdfPageEventHelper {

    private final PdfRenderOptions.HeaderFooterOptions options;
    private final Font font;
    @Getter
    @Setter
    private int totalPages = 0;

    public HeaderFooterEventHandler(PdfRenderOptions.HeaderFooterOptions options, BaseFont baseFont) {
        this.options = options;
        this.font = new Font(baseFont, options.getFontSize());
    }

    @Override
    public void onOpenDocument(PdfWriter writer, Document document) {
        // 文档打开时初始化
    }

    @Override
    public void onStartPage(PdfWriter writer, Document document) {
        // 页面开始时处理页眉
        addHeader(writer, document);
    }

    @Override
    public void onEndPage(PdfWriter writer, Document document) {
        // 页面结束时处理页脚
        addFooter(writer, document);
    }

    @Override
    public void onCloseDocument(PdfWriter writer, Document document) {
        // 文档关闭时更新总页数
        this.totalPages = writer.getPageNumber() - 1;
    }

    /**
     * 添加页眉
     */
    private void addHeader(PdfWriter writer, Document document) {
        if (options == null) return;

        PdfContentByte cb = writer.getDirectContent();
        Rectangle pageSize = document.getPageSize();

        float headerY = pageSize.getTop() - document.topMargin() + 20;

        // 左侧页眉
        if (!Str.isEmpty(options.getHeaderLeft())) {
            addText(cb, options.getHeaderLeft(), document.leftMargin(), headerY, Element.ALIGN_LEFT);
        }

        // 中间页眉
        if (!Str.isEmpty(options.getHeaderCenter())) {
            float centerX = (pageSize.getLeft() + pageSize.getRight()) / 2;
            addText(cb, options.getHeaderCenter(), centerX, headerY, Element.ALIGN_CENTER);
        }

        // 右侧页眉
        if (!Str.isEmpty(options.getHeaderRight())) {
            addText(cb, options.getHeaderRight(), pageSize.getRight() - document.rightMargin(), headerY, Element.ALIGN_RIGHT);
        }
    }

    /**
     * 添加页角
     */
    private void addFooter(PdfWriter writer, Document document) {
        if (options == null) return;

        PdfContentByte cb = writer.getDirectContent();
        Rectangle pageSize = document.getPageSize();

        float footerY = document.bottomMargin() - 20;
        int currentPage = writer.getPageNumber();

        // 左侧页脚
        if (!Str.isEmpty(options.getFooterLeft())) {
            String text = processPageNumberTemplate(options.getFooterLeft(), currentPage);
            addText(cb, text, document.leftMargin(), footerY, Element.ALIGN_LEFT);
        }

        // 中间页脚
        if (!Str.isEmpty(options.getFooterCenter())) {
            String text = processPageNumberTemplate(options.getFooterCenter(), currentPage);
            float centerX = (pageSize.getLeft() + pageSize.getRight()) / 2;
            addText(cb, text, centerX, footerY, Element.ALIGN_CENTER);
        }

        // 右侧页脚
        if (!Str.isEmpty(options.getFooterRight())) {
            String text = processPageNumberTemplate(options.getFooterRight(), currentPage);
            addText(cb, text, pageSize.getRight() - document.rightMargin(), footerY, Element.ALIGN_RIGHT);
        }


        // 显示页码（如果启用且没有在其他位置显示）
        if (options.isShowPageNumbers() &&
                Str.isEmpty(options.getFooterLeft()) &&
                Str.isEmpty(options.getFooterCenter()) &&
                Str.isEmpty(options.getFooterRight())) {
            String pageText = processPageNumberTemplate(options.getPageNumberFormat(), currentPage);
            float centerX = (pageSize.getLeft() + pageSize.getRight()) / 2;
            addText(cb, pageText, centerX, footerY, Element.ALIGN_CENTER);
        }
    }

    /**
     * 添加文本
     */
    private void addText(PdfContentByte cb, String text, float x, float y, int alignment) {
        try {
            Phrase phrase = new Phrase(text, font);
            ColumnText.showTextAligned(cb, alignment, phrase, x, y, 0);
        } catch (Exception e) {
            log.error("添加PDF文本失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 处理页码
     */
    private String processPageNumberTemplate(String template, int currentPage) {
        if (Str.isEmpty(template)) {
            return "";
        }

        String result = template.replace("{page}", String.valueOf(currentPage));

        // 注意：总页数在文档生成完成前无法准确获取，这里暂时用占位符
        if (totalPages > 0) {
            result = result.replace("{total}", String.valueOf(totalPages));
        } else {
            result = result.replace("{total}", "?");
        }

        return result;
    }
}

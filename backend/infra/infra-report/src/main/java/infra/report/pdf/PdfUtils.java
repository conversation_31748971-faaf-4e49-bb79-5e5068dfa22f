package infra.report.pdf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;

/**
 * PDF工具类 - 提供简便的PDF生成方法
 */
@Slf4j
public class PdfUtils {

    /**
     * 从HTML生成PDF
     * 
     * @param html HTML内容
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html) {
        return htmlToPdf(html, null, new PdfRenderOptions());
    }

    /**
     * 从HTML生成PDF（自定义选项）
     * 
     * @param html HTML内容
     * @param options PDF渲染选项
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, PdfRenderOptions options) {
        return htmlToPdf(html, null, options);
    }

    /**
     * 从HTML生成PDF（自定义CSS）
     * 
     * @param html HTML内容
     * @param css CSS内容
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, String css) {
        return htmlToPdf(html, css, new PdfRenderOptions());
    }

    /**
     * 从HTML生成PDF（自定义选项和CSS）
     * 
     * @param html HTML内容
     * @param css CSS内容
     * @param options PDF渲染选项
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, String css, PdfRenderOptions options) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
            return generator.generatePdf(html, css);
        } catch (Exception e) {
            log.error("HTML转PDF失败", e);
            throw new RuntimeException("HTML转PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从HTML生成PDF到输出流
     * 
     * @param html HTML内容
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, OutputStream outputStream) {
        htmlToPdf(html, null, new PdfRenderOptions(), outputStream);
    }

    /**
     * 从HTML生成PDF到输出流（自定义选项）
     * 
     * @param html HTML内容
     * @param options PDF渲染选项
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, PdfRenderOptions options, OutputStream outputStream) {
        htmlToPdf(html, null, options, outputStream);
    }

    /**
     * 从HTML生成PDF到输出流（自定义CSS）
     * 
     * @param html HTML内容
     * @param css CSS内容
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, String css, OutputStream outputStream) {
        htmlToPdf(html, css, new PdfRenderOptions(), outputStream);
    }

    /**
     * 从HTML生成PDF到输出流（自定义选项和CSS）
     * 
     * @param html HTML内容
     * @param css CSS内容
     * @param options PDF渲染选项
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, String css, PdfRenderOptions options, OutputStream outputStream) {
        if (StringUtils.isBlank(html)) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw
</augment_code_snippet>
package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;

import java.awt.Color;
import java.io.File;
import java.io.InputStream;
import java.net.URI;

/**
 * 水印处理器
 */
@Slf4j
public class WatermarkProcessor {
    /**
     * 添加水印到页面
     */
    public static void addWatermark(PdfWriter writer, Document document,
                                    PdfRenderOptions.WatermarkOptions watermarkOptions,
                                    BaseFont baseFont) {
        if (watermarkOptions == null) {
            return;
        }

        if (!Str.isEmpty(watermarkOptions.getText())) {
            addTextWatermark(writer, document, watermarkOptions, baseFont);
        }

        if (!Str.isEmpty(watermarkOptions.getImagePath())) {
            addImageWatermark(writer, document, watermarkOptions);
        }
    }

    /**
     * 添加文字水印
     */
    private static void addTextWatermark(PdfWriter writer, Document document,
                                         PdfRenderOptions.WatermarkOptions options, BaseFont baseFont) {
        try {
            PdfContentByte cb = writer.getDirectContentUnder();
            cb.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            cb.setGState(gState);

            // 解析颜色
            Color color = parseColor(options.getColor());
            cb.setColorFill(new Color(color.getRed(), color.getGreen(), color.getBlue()));

            // 设置字体
            cb.beginText();
            cb.setFontAndSize(baseFont, options.getFontSize());

            // 计算水印位置
            Rectangle pageSize = document.getPageSize();
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 添加文字水印
            cb.showTextAligned(Element.ALIGN_CENTER, options.getText(),
                    position[0], position[1], options.getRotation());

            cb.endText();
            cb.restoreState();

        } catch (Exception e) {
            log.error("添加文本水印失败", e);
        }
    }

    /**
     * 添加图片水印
     */
    private static void addImageWatermark(PdfWriter writer, Document document,
                                          PdfRenderOptions.WatermarkOptions options) {
        try {
            Image image = loadWatermarkImage(options.getImagePath());
            if (image == null) {
                log.warn("加载图片水印失败: {}", options.getImagePath());
                return;
            }

            PdfContentByte cb = writer.getDirectContentUnder();
            cb.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            cb.setGState(gState);

            // 计算图片位置和大小
            Rectangle pageSize = document.getPageSize();
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 调整图片大小（保持比例，最大不超过页面的1/3）
            float maxWidth = pageSize.getWidth() / 3;
            float maxHeight = pageSize.getHeight() / 3;

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                image.scaleToFit(maxWidth, maxHeight);
            }

            // 调整位置以居中图片
            float x = position[0] - image.getScaledWidth() / 2;
            float y = position[1] - image.getScaledHeight() / 2;

            image.setAbsolutePosition(x, y);
            image.setRotationDegrees(options.getRotation());

            cb.addImage(image);
            cb.restoreState();

        } catch (Exception e) {
            log.error("添加图片水印失败", e);
        }
    }

    /**
     * 加载水印图片（支持多种来源）
     */
    private static Image loadWatermarkImage(String imagePath) {
        try {
            if (Str.isEmpty(imagePath)) {
                return null;
            }

            // 支持 HTTP/HTTPS URL
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return Image.getInstance(new URI(imagePath).toURL());
            }

            // 支持 classpath 资源
            if (imagePath.startsWith("classpath:")) {
                String resourcePath = imagePath.substring("classpath:".length());
                InputStream inputStream = WatermarkProcessor.class.getClassLoader().getResourceAsStream(resourcePath);
                if (inputStream != null) {
                    byte[] imageData = inputStream.readAllBytes();
                    inputStream.close();
                    return Image.getInstance(imageData);
                }
            }

            // 支持本地文件路径
            File file = new File(imagePath);
            if (file.exists()) {
                return Image.getInstance(imagePath);
            }

            // 尝试作为资源路径加载
            InputStream inputStream = WatermarkProcessor.class.getClassLoader().getResourceAsStream(imagePath);
            if (inputStream != null) {
                byte[] imageData = inputStream.readAllBytes();
                inputStream.close();
                return Image.getInstance(imageData);
            }

            log.warn("无法找到图片资源: {}", imagePath);
            return null;

        } catch (Exception e) {
            log.error("加载图片失败: {}", imagePath, e);
            return null;
        }
    }

    /**
     * 计算水印位置
     */
    private static float[] calculateWatermarkPosition(Rectangle pageSize, String position) {
        float x, y;

        y = switch (position.toLowerCase()) {
            case "top-left" -> {
                x = pageSize.getWidth() * 0.25f;
                yield pageSize.getHeight() * 0.75f;
            }
            case "top-right" -> {
                x = pageSize.getWidth() * 0.75f;
                yield pageSize.getHeight() * 0.75f;
            }
            case "bottom-left" -> {
                x = pageSize.getWidth() * 0.25f;
                yield pageSize.getHeight() * 0.25f;
            }
            case "bottom-right" -> {
                x = pageSize.getWidth() * 0.75f;
                yield pageSize.getHeight() * 0.25f;
            }
            default -> {
                x = pageSize.getWidth() / 2;
                yield pageSize.getHeight() / 2;
            }
        };

        return new float[]{x, y};
    }

    /**
     * 解析颜色字符串
     */
    private static Color parseColor(String colorStr) {
        if (Str.isEmpty(colorStr)) {
            return Color.LIGHT_GRAY;
        }

        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                return switch (colorStr.toLowerCase()) {
                    case "red" -> Color.RED;
                    case "green" -> Color.GREEN;
                    case "blue" -> Color.BLUE;
                    case "black" -> Color.BLACK;
                    case "white" -> Color.WHITE;
                    case "gray" -> Color.GRAY;
                    default -> Color.LIGHT_GRAY;
                };
            }
        } catch (Exception e) {
            log.warn("解析颜色失败: {}, 将使用默认颜色", colorStr);
            return Color.LIGHT_GRAY;
        }
    }
}

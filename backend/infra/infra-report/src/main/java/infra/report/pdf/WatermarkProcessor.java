package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;

import java.awt.Color;
import java.io.File;
import java.io.InputStream;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 水印处理器
 */
@Slf4j
public class WatermarkProcessor {
    // 图片缓存
    private static final Map<String, Image> IMAGE_CACHE = new ConcurrentHashMap<>();

    /**
     * 添加水印到页面
     */
    public static void addWatermark(PdfContentByte canvas, Rectangle pageSize,
                                    PdfRenderOptions.WatermarkOptions watermarkOptions) {
        if (watermarkOptions == null) {
            return;
        }

        if (!Str.isEmpty(watermarkOptions.getText())) {
            addTextWatermark(canvas, pageSize, watermarkOptions);
        }

        if (!Str.isEmpty(watermarkOptions.getImagePath())) {
            addImageWatermark(canvas, pageSize, watermarkOptions);
        }
    }

    /**
     * 添加水印到页面
     */
    public static void addWatermark(PdfWriter writer, Document document,
                                    PdfRenderOptions.WatermarkOptions watermarkOptions,
                                    BaseFont baseFont) {
        if (watermarkOptions == null) {
            return;
        }

        PdfContentByte canvas = writer.getDirectContentUnder();
        Rectangle pageSize = document.getPageSize();

        if (!Str.isEmpty(watermarkOptions.getText())) {
            if (watermarkOptions.isRepeatWatermark()) {
                addRepeatedTextWatermark(canvas, pageSize, watermarkOptions, baseFont);
            } else {
                addSingleTextWatermark(canvas, pageSize, watermarkOptions, baseFont);
            }
        }

        if (!Str.isEmpty(watermarkOptions.getImagePath())) {
            addImageWatermark(canvas, pageSize, watermarkOptions);
        }
    }

    /**
     * 添加文字水印（使用PdfContentByte）
     */
    private static void addTextWatermark(PdfContentByte canvas, Rectangle pageSize,
                                         PdfRenderOptions.WatermarkOptions options) {
        try {
            // 创建默认字体
            BaseFont baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);

            if (options.isRepeatWatermark()) {
                addRepeatedTextWatermark(canvas, pageSize, options, baseFont);
            } else {
                addSingleTextWatermark(canvas, pageSize, options, baseFont);
            }
        } catch (Exception e) {
            log.warn("创建默认字体失败，跳过文本水印", e);
        }
    }

    /**
     * 添加单个文字水印
     */
    private static void addSingleTextWatermark(PdfContentByte canvas, Rectangle pageSize,
                                               PdfRenderOptions.WatermarkOptions options, BaseFont baseFont) {
        try {
            canvas.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            canvas.setGState(gState);

            // 解析颜色
            Color color = parseColor(options.getColor());
            canvas.setColorFill(color);

            // 设置字体
            canvas.beginText();
            canvas.setFontAndSize(baseFont, options.getFontSize());

            // 计算水印位置
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 添加文字水印
            canvas.showTextAligned(Element.ALIGN_CENTER, options.getText(),
                    position[0], position[1], options.getRotation());

            canvas.endText();
            canvas.restoreState();

        } catch (Exception e) {
            log.warn("添加文本水印失败", e);
        }
    }

    /**
     * 添加重复文字水印（覆盖整个页面）
     */
    private static void addRepeatedTextWatermark(PdfContentByte canvas, Rectangle pageSize,
                                                 PdfRenderOptions.WatermarkOptions options, BaseFont baseFont) {
        try {
            canvas.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            canvas.setGState(gState);

            // 解析颜色
            Color color = parseColor(options.getColor());
            canvas.setColorFill(color);

            // 设置字体
            canvas.beginText();
            canvas.setFontAndSize(baseFont, options.getFontSize());

            // 计算间距（考虑稀疏度）
            float horizontalSpacing = options.getHorizontalSpacing() / options.getDensity();
            float verticalSpacing = options.getVerticalSpacing() / options.getDensity();

            // 计算页面范围
            float pageWidth = pageSize.getWidth();
            float pageHeight = pageSize.getHeight();

            // 计算起始位置（从负坐标开始，确保覆盖整个页面）
            float startX = -horizontalSpacing;
            float startY = -verticalSpacing;

            // 循环添加水印
            for (float y = startY; y <= pageHeight + verticalSpacing; y += verticalSpacing) {
                for (float x = startX; x <= pageWidth + horizontalSpacing; x += horizontalSpacing) {
                    // 添加水印文本
                    canvas.showTextAligned(Element.ALIGN_CENTER, options.getText(), x, y, options.getRotation());
                }
            }

            canvas.endText();
            canvas.restoreState();

            log.debug("添加重复文字水印成功，间距: {}x{}, 稀疏度: {}",
                    horizontalSpacing, verticalSpacing, options.getDensity());

        } catch (Exception e) {
            log.warn("添加重复文字水印失败", e);
        }
    }

    /**
     * 添加图片水印（使用PdfContentByte）
     */
    private static void addImageWatermark(PdfContentByte canvas, Rectangle pageSize,
                                          PdfRenderOptions.WatermarkOptions options) {
        if (options.isRepeatWatermark()) {
            addRepeatedImageWatermark(canvas, pageSize, options);
        } else {
            addSingleImageWatermark(canvas, pageSize, options);
        }
    }

    /**
     * 添加单个图片水印
     */
    private static void addSingleImageWatermark(PdfContentByte canvas, Rectangle pageSize,
                                                PdfRenderOptions.WatermarkOptions options) {
        try {
            Image image = loadWatermarkImage(options.getImagePath());
            if (image == null) {
                return;
            }

            canvas.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            canvas.setGState(gState);

            // 计算图片位置和大小
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 调整图片大小（保持比例，最大不超过页面的1/3）
            float maxWidth = pageSize.getWidth() / 3;
            float maxHeight = pageSize.getHeight() / 3;

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                image.scaleToFit(maxWidth, maxHeight);
            }

            // 调整位置以居中图片
            float x = position[0] - image.getScaledWidth() / 2;
            float y = position[1] - image.getScaledHeight() / 2;

            image.setAbsolutePosition(x, y);
            image.setRotationDegrees(options.getRotation());

            canvas.addImage(image);
            canvas.restoreState();

        } catch (Exception e) {
            log.error("添加图片水印失败", e);
        }
    }

    /**
     * 添加重复图片水印（覆盖整个页面）
     */
    private static void addRepeatedImageWatermark(PdfContentByte canvas, Rectangle pageSize,
                                                  PdfRenderOptions.WatermarkOptions options) {
        try {
            Image image = loadWatermarkImage(options.getImagePath());
            if (image == null) {
                return;
            }

            canvas.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            canvas.setGState(gState);

            // 调整图片大小（保持比例，适合重复显示）
            float maxWidth = Math.min(options.getHorizontalSpacing() * 0.8f, pageSize.getWidth() / 6);
            float maxHeight = Math.min(options.getVerticalSpacing() * 0.8f, pageSize.getHeight() / 6);

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                image.scaleToFit(maxWidth, maxHeight);
            }

            // 计算间距（考虑稀疏度）
            float horizontalSpacing = options.getHorizontalSpacing() / options.getDensity();
            float verticalSpacing = options.getVerticalSpacing() / options.getDensity();

            // 计算页面范围
            float pageWidth = pageSize.getWidth();
            float pageHeight = pageSize.getHeight();

            // 计算起始位置
            float startX = -horizontalSpacing;
            float startY = -verticalSpacing;

            // 循环添加图片水印
            for (float y = startY; y <= pageHeight + verticalSpacing; y += verticalSpacing) {
                for (float x = startX; x <= pageWidth + horizontalSpacing; x += horizontalSpacing) {
                    try {
                        // 创建图片副本以避免位置冲突
                        Image imageInstance = Image.getInstance(image);

                        // 调整位置以居中图片
                        float imgX = x - imageInstance.getScaledWidth() / 2;
                        float imgY = y - imageInstance.getScaledHeight() / 2;

                        imageInstance.setAbsolutePosition(imgX, imgY);
                        imageInstance.setRotationDegrees(options.getRotation());

                        canvas.addImage(imageInstance);
                    } catch (Exception e) {
                        log.debug("添加单个重复图片水印失败: x={}, y={}", x, y);
                    }
                }
            }

            canvas.restoreState();
        } catch (Exception e) {
            log.error("添加重复图片水印失败", e);
        }
    }

    /**
     * 加载水印图片
     */
    private static Image loadWatermarkImage(String imagePath) {
        if (Str.isEmpty(imagePath)) {
            return null;
        }

        return IMAGE_CACHE.computeIfAbsent(imagePath, path -> {
            try {
                return loadImageFromPath(path);
            } catch (Exception e) {
                log.error("加载水印图片失败: {}", path, e);
                return null;
            }
        });
    }

    /**
     * 加载水印图片（支持多种来源）
     */
    private static Image loadImageFromPath(String imagePath) {
        try {
            // 支持 HTTP/HTTPS URL
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return Image.getInstance(new URI(imagePath).toURL());
            }

            // 防止路径遍历攻击
            if (imagePath.contains("..") || imagePath.contains("//")) {
                log.warn("检测到不安全的图片路径: {}", imagePath);
                return null;
            }

            // 支持 classpath 资源
            if (imagePath.startsWith("classpath:")) {
                String resourcePath = imagePath.substring("classpath:".length());
                InputStream inputStream = WatermarkProcessor.class.getClassLoader().getResourceAsStream(resourcePath);
                if (inputStream != null) {
                    byte[] imageData = inputStream.readAllBytes();
                    inputStream.close();
                    return Image.getInstance(imageData);
                }
            }

            // 支持本地文件路径
            File file = new File(imagePath);
            if (file.exists()) {
                return Image.getInstance(imagePath);
            }

            // 尝试作为资源路径加载
            InputStream inputStream = WatermarkProcessor.class.getClassLoader().getResourceAsStream(imagePath);
            if (inputStream != null) {
                byte[] imageData = inputStream.readAllBytes();
                inputStream.close();
                return Image.getInstance(imageData);
            }

            log.warn("无法找到水印图片资源: {}", imagePath);
            return null;

        } catch (Exception e) {
            log.error("加载水印图片失败: {}", imagePath, e);
            return null;
        }
    }

    /**
     * 计算水印位置
     */
    private static float[] calculateWatermarkPosition(Rectangle pageSize, String position) {
        float x, y;

        y = switch (position.toLowerCase()) {
            case "top-left" -> {
                x = pageSize.getWidth() * 0.25f;
                yield pageSize.getHeight() * 0.75f;
            }
            case "top-right" -> {
                x = pageSize.getWidth() * 0.75f;
                yield pageSize.getHeight() * 0.75f;
            }
            case "bottom-left" -> {
                x = pageSize.getWidth() * 0.25f;
                yield pageSize.getHeight() * 0.25f;
            }
            case "bottom-right" -> {
                x = pageSize.getWidth() * 0.75f;
                yield pageSize.getHeight() * 0.25f;
            }
            default -> {
                x = pageSize.getWidth() / 2;
                yield pageSize.getHeight() / 2;
            }
        };

        return new float[]{x, y};
    }

    /**
     * 解析颜色字符串
     */
    private static Color parseColor(String colorStr) {
        if (Str.isEmpty(colorStr)) {
            return Color.LIGHT_GRAY;
        }

        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                return switch (colorStr.toLowerCase()) {
                    case "red" -> Color.RED;
                    case "green" -> Color.GREEN;
                    case "blue" -> Color.BLUE;
                    case "black" -> Color.BLACK;
                    case "white" -> Color.WHITE;
                    case "gray" -> Color.GRAY;
                    default -> Color.LIGHT_GRAY;
                };
            }
        } catch (Exception e) {
            log.warn("解析颜色失败: {}, 将使用默认颜色", colorStr);
            return Color.LIGHT_GRAY;
        }
    }
}

package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.awt.Color;
import java.net.URL;

/**
 * 水印处理器
 */
@Slf4j
public class WatermarkProcessor {
    /**
     * 添加水印到页面
     */
    public static void addWatermark(PdfWriter writer, Document document,
                                    PdfRenderOptions.WatermarkOptions watermarkOptions,
                                    BaseFont baseFont) {
        if (watermarkOptions == null) {
            return;
        }

        PdfContentByte cb = writer.getDirectContentUnder();
        Rectangle pageSize = document.getPageSize();
        PdfGState gState = new PdfGState();
        gState.setFillOpacity(watermarkOptions.getOpacity());
        cb.setGState(gState);

        // 添加文本水印
        if (StringUtils.isNotBlank(watermarkOptions.getText())) {
            addTextWatermark(cb, pageSize, watermarkOptions, baseFont);
        }

        // 添加图片水印
        if (StringUtils.isNotBlank(watermarkOptions.getImagePath())) {
            addImageWatermark(cb, pageSize, watermarkOptions);
        }
    }

    /**
     * 添加文字水印
     */
    private static void addTextWatermark(PdfContentByte cb, Rectangle pageSize,
                                         PdfRenderOptions.WatermarkOptions options, BaseFont baseFont) {
        try {
            cb.saveState();

            // 解析颜色
            Color color = parseColor(options.getColor());
            cb.setColorFill(new BaseColor(color.getRed(), color.getGreen(), color.getBlue()));

            // 设置字体
            cb.beginText();
            cb.setFontAndSize(baseFont, options.getFontSize());

            // 计算水印位置
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 添加文字水印
            cb.showTextAligned(Element.ALIGN_CENTER, options.getText(),
                    position[0], position[1], options.getRotation());

            cb.endText();
            cb.restoreState();
        } catch (Exception e) {
            log.error("添加文本水印失败", e);
        }
    }

    /**
     * 添加图片水印
     */
    private static void addImageWatermark(PdfContentByte cb, Rectangle pageSize,
                                          PdfRenderOptions.WatermarkOptions options) {
        try {
            URL imageUrl = WatermarkProcessor.class.getClassLoader().getResource(options.getImagePath());
            if (imageUrl == null) {
                log.warn("水印图片未找到: {}", options.getImagePath());
                return;
            }

            Image image = Image.getInstance(imageUrl);
            if (image == null) {
                log.warn("创建水印图片对象失败: {}", options.getImagePath());
                return;
            }

            cb.saveState();

            // 计算图片位置和大小
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 调整图片大小（保持比例，最大不超过页面的1/3）
            float maxWidth = pageSize.getWidth() / 3;
            float maxHeight = pageSize.getHeight() / 3;

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                image.scaleToFit(maxWidth, maxHeight);
            }

            // 设置图片位置和旋转
            image.setAbsolutePosition(
                    position[0] - image.getScaledWidth() / 2,
                    position[1] - image.getScaledHeight() / 2);
            image.setRotationDegrees(options.getRotation());

            // 添加图片
            cb.addImage(image);
            cb.restoreState();
        } catch (Exception e) {
            log.error("添加图片水印失败", e);
        }
    }

    /**
     * 计算水印位置
     */
    private static float[] calculateWatermarkPosition(Rectangle pageSize, String position) {
        float x = pageSize.getWidth() / 2;
        float y = pageSize.getHeight() / 2;

        switch (position.toLowerCase()) {
            case "top-left":
                x = pageSize.getLeft() + 50;
                y = pageSize.getTop() - 50;
                break;
            case "top-right":
                x = pageSize.getRight() - 50;
                y = pageSize.getTop() - 50;
                break;
            case "bottom-left":
                x = pageSize.getLeft() + 50;
                y = pageSize.getBottom() + 50;
                break;
            case "bottom-right":
                x = pageSize.getRight() - 50;
                y = pageSize.getBottom() + 50;
                break;
            default: // center
                break;
        }

        return new float[]{x, y};
    }

    /**
     * 解析颜色
     */
    private static Color parseColor(String colorStr) {
        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                // 尝试解析颜色名称
                return (Color) Color.class.getField(colorStr.toUpperCase()).get(null);
            }
        } catch (Exception e) {
            log.warn("解析颜色失败: {}, 使用默认颜色", colorStr);
            return Color.LIGHT_GRAY;
        }
    }
}

package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;

import java.awt.Color;
import java.io.File;
import java.io.InputStream;
import java.net.URI;

/**
 * 水印处理器
 */
@Slf4j
public class WatermarkProcessor {
    /**
     * 添加水印到页面（用于PdfStamper）
     */
    public static void addWatermark(PdfContentByte canvas, Rectangle pageSize,
                                    PdfRenderOptions.WatermarkOptions watermarkOptions) {
        if (watermarkOptions == null) {
            return;
        }

        if (!Str.isEmpty(watermarkOptions.getText())) {
            addTextWatermark(canvas, pageSize, watermarkOptions);
        }

        if (!Str.isEmpty(watermarkOptions.getImagePath())) {
            addImageWatermark(canvas, pageSize, watermarkOptions);
        }
    }

    /**
     * 添加水印到页面（用于PdfWriter - 兼容旧版本）
     */
    public static void addWatermark(PdfWriter writer, Document document,
                                    PdfRenderOptions.WatermarkOptions watermarkOptions,
                                    BaseFont baseFont) {
        if (watermarkOptions == null) {
            return;
        }

        PdfContentByte canvas = writer.getDirectContentUnder();
        Rectangle pageSize = document.getPageSize();

        if (!Str.isEmpty(watermarkOptions.getText())) {
            addTextWatermark(canvas, pageSize, watermarkOptions, baseFont);
        }

        if (!Str.isEmpty(watermarkOptions.getImagePath())) {
            addImageWatermark(canvas, pageSize, watermarkOptions);
        }
    }

    /**
     * 添加文字水印（新版本 - 使用PdfContentByte）
     */
    private static void addTextWatermark(PdfContentByte canvas, Rectangle pageSize,
                                         PdfRenderOptions.WatermarkOptions options) {
        try {
            // 创建默认字体
            BaseFont baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            addTextWatermark(canvas, pageSize, options, baseFont);
        } catch (Exception e) {
            log.error("创建默认字体失败，跳过文本水印", e);
        }
    }

    /**
     * 添加文字水印（带自定义字体）
     */
    private static void addTextWatermark(PdfContentByte canvas, Rectangle pageSize,
                                         PdfRenderOptions.WatermarkOptions options, BaseFont baseFont) {
        try {
            canvas.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            canvas.setGState(gState);

            // 解析颜色
            Color color = parseColor(options.getColor());
            canvas.setColorFill(color);

            // 设置字体
            canvas.beginText();
            canvas.setFontAndSize(baseFont, options.getFontSize());

            // 计算水印位置
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 添加文字水印
            canvas.showTextAligned(Element.ALIGN_CENTER, options.getText(),
                    position[0], position[1], options.getRotation());

            canvas.endText();
            canvas.restoreState();

        } catch (Exception e) {
            log.error("添加文本水印失败", e);
        }
    }

    /**
     * 添加图片水印（新版本 - 使用PdfContentByte）
     */
    private static void addImageWatermark(PdfContentByte canvas, Rectangle pageSize,
                                          PdfRenderOptions.WatermarkOptions options) {
        try {
            Image image = loadWatermarkImage(options.getImagePath());
            if (image == null) {
                log.warn("加载图片水印失败: {}", options.getImagePath());
                return;
            }

            canvas.saveState();

            // 设置透明度
            PdfGState gState = new PdfGState();
            gState.setFillOpacity(options.getOpacity());
            canvas.setGState(gState);

            // 计算图片位置和大小
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 调整图片大小（保持比例，最大不超过页面的1/3）
            float maxWidth = pageSize.getWidth() / 3;
            float maxHeight = pageSize.getHeight() / 3;

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                image.scaleToFit(maxWidth, maxHeight);
            }

            // 调整位置以居中图片
            float x = position[0] - image.getScaledWidth() / 2;
            float y = position[1] - image.getScaledHeight() / 2;

            image.setAbsolutePosition(x, y);
            image.setRotationDegrees(options.getRotation());

            canvas.addImage(image);
            canvas.restoreState();

        } catch (Exception e) {
            log.error("添加图片水印失败", e);
        }
    }

    /**
     * 加载水印图片（支持多种来源）
     */
    private static Image loadWatermarkImage(String imagePath) {
        try {
            if (Str.isEmpty(imagePath)) {
                return null;
            }

            // 支持 HTTP/HTTPS URL
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return Image.getInstance(new URI(imagePath).toURL());
            }

            // 支持 classpath 资源
            if (imagePath.startsWith("classpath:")) {
                String resourcePath = imagePath.substring("classpath:".length());
                InputStream inputStream = WatermarkProcessor.class.getClassLoader().getResourceAsStream(resourcePath);
                if (inputStream != null) {
                    byte[] imageData = inputStream.readAllBytes();
                    inputStream.close();
                    return Image.getInstance(imageData);
                }
            }

            // 支持本地文件路径
            File file = new File(imagePath);
            if (file.exists()) {
                return Image.getInstance(imagePath);
            }

            // 尝试作为资源路径加载
            InputStream inputStream = WatermarkProcessor.class.getClassLoader().getResourceAsStream(imagePath);
            if (inputStream != null) {
                byte[] imageData = inputStream.readAllBytes();
                inputStream.close();
                return Image.getInstance(imageData);
            }

            log.warn("无法找到图片资源: {}", imagePath);
            return null;

        } catch (Exception e) {
            log.error("加载图片失败: {}", imagePath, e);
            return null;
        }
    }

    /**
     * 计算水印位置
     */
    private static float[] calculateWatermarkPosition(Rectangle pageSize, String position) {
        float x, y;

        y = switch (position.toLowerCase()) {
            case "top-left" -> {
                x = pageSize.getWidth() * 0.25f;
                yield pageSize.getHeight() * 0.75f;
            }
            case "top-right" -> {
                x = pageSize.getWidth() * 0.75f;
                yield pageSize.getHeight() * 0.75f;
            }
            case "bottom-left" -> {
                x = pageSize.getWidth() * 0.25f;
                yield pageSize.getHeight() * 0.25f;
            }
            case "bottom-right" -> {
                x = pageSize.getWidth() * 0.75f;
                yield pageSize.getHeight() * 0.25f;
            }
            default -> {
                x = pageSize.getWidth() / 2;
                yield pageSize.getHeight() / 2;
            }
        };

        return new float[]{x, y};
    }

    /**
     * 解析颜色字符串
     */
    private static Color parseColor(String colorStr) {
        if (Str.isEmpty(colorStr)) {
            return Color.LIGHT_GRAY;
        }

        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                return switch (colorStr.toLowerCase()) {
                    case "red" -> Color.RED;
                    case "green" -> Color.GREEN;
                    case "blue" -> Color.BLUE;
                    case "black" -> Color.BLACK;
                    case "white" -> Color.WHITE;
                    case "gray" -> Color.GRAY;
                    default -> Color.LIGHT_GRAY;
                };
            }
        } catch (Exception e) {
            log.warn("解析颜色失败: {}, 将使用默认颜色", colorStr);
            return Color.LIGHT_GRAY;
        }
    }
}

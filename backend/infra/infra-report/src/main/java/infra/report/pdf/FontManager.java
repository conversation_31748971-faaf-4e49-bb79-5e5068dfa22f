package infra.report.pdf;

import com.lowagie.text.FontFactory;
import infra.core.text.Str;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字体管理器 - 支持外部字体目录和多种加载策略
 */
@Slf4j
public class FontManager {

    /**
     * 默认外部字体目录
     */
    private static final String DEFAULT_EXTERNAL_FONT_DIR = "/app/fonts";

    /**
     * 环境变量名
     */
    private static final String FONT_PATH_ENV = "FONT_PATH";

    /**
     * 系统属性名
     */
    private static final String FONT_PATH_PROPERTY = "font.path";

    /**
     * 支持的字体文件扩展名
     */
    private static final Set<String> SUPPORTED_FONT_EXTENSIONS = Set.of(
            ".ttf", ".ttc", ".otf", ".woff", ".woff2"
    );

    /**
     * 常用中文字体文件名（不含扩展名）
     */
    private static final List<String> COMMON_CHINESE_FONTS = List.of(
            "SimHei", "SimSun", "NotoSansCJK-Regular", "SourceHanSans-Regular",
            "PingFang", "MicrosoftYaHei", "DroidSansFallback", "SourceHanSansCN-Regular",
            "NotoSansSC-Regular", "AlibabaPuHuiTi-Regular"
    );

    /**
     * 已注册的字体缓存
     */
    private final Map<String, String> registeredFonts = new ConcurrentHashMap<>();

    /**
     * 单例实例
     */
    private static volatile FontManager instance;

    /**
     * 初始化状态
     */
    @Getter
    private volatile boolean initialized = false;

    /**
     * 初始化锁
     */
    private final Object initLock = new Object();

    /**
     * 当前字体路径
     */
    @Getter
    private volatile String currentFontPath;

    private FontManager() {
        // 私有构造函数
    }

    /**
     * 获取单例实例
     */
    public static FontManager getInstance() {
        if (instance == null) {
            synchronized (FontManager.class) {
                if (instance == null) {
                    instance = new FontManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化字体管理器
     */
    public void initialize() {
        initialize(null);
    }

    /**
     * 初始化字体管理器（支持自定义字体路径）
     * 使用双重检查锁定确保线程安全且只初始化一次
     */
    public void initialize(String customFontPath) {
        // 快速检查，避免不必要的同步
        if (initialized && Objects.equals(currentFontPath, customFontPath)) {
            log.debug("字体管理器已初始化，跳过重复初始化");
            return;
        }

        synchronized (initLock) {
            // 双重检查
            if (initialized && Objects.equals(currentFontPath, customFontPath)) {
                return;
            }

            log.info("开始初始化字体管理器，字体路径: {}", customFontPath);

            // 如果字体路径发生变化，需要重新初始化
            if (initialized && !Objects.equals(currentFontPath, customFontPath)) {
                log.info("字体路径发生变化，重新初始化字体管理器");
                registeredFonts.clear();
            }

            // 记录当前字体路径
            currentFontPath = customFontPath;

            // 按优先级加载字体
            loadFontsFromExternalDirectory(customFontPath);
            loadFontsFromClasspath();
            loadSystemFonts();

            // 标记为已初始化
            initialized = true;

            log.info("字体管理器初始化完成，共注册 {} 个字体", registeredFonts.size());

            // 输出已注册的字体信息
            if (log.isDebugEnabled()) {
                registeredFonts.forEach((name, path) ->
                        log.debug("已注册字体: {} -> {}", name, path));
            }
        }
    }

    /**
     * 强制重新初始化（主要用于测试或特殊场景）
     */
    public void forceReinitialize(String customFontPath) {
        synchronized (initLock) {
            log.info("强制重新初始化字体管理器");
            initialized = false;
            registeredFonts.clear();
            initialize(customFontPath);
        }
    }

    /**
     * 从外部目录加载字体
     */
    private void loadFontsFromExternalDirectory(String customFontPath) {
        String fontPath = !Str.isEmpty(customFontPath) ? customFontPath : getFontPath();
        if (Str.isEmpty(fontPath)) {
            log.debug("未配置外部字体目录");
            return;
        }

        Path fontDir = Paths.get(fontPath);
        if (!Files.exists(fontDir) || !Files.isDirectory(fontDir)) {
            log.warn("外部字体目录不存在或不是目录: {}", fontPath);
            return;
        }

        log.info("从外部目录加载字体: {}", fontPath);

        try (var walk = Files.walk(fontDir)) {
            walk.filter(Files::isRegularFile)
                    .filter(this::isSupportedFontFile)
                    .forEach(this::registerFontFile);
        } catch (Exception e) {
            log.error("从外部目录加载字体失败: {}", fontPath, e);
        }
    }

    /**
     * 从classpath加载字体
     */
    private void loadFontsFromClasspath() {
        log.debug("从classpath加载字体...");

        for (String fontName : COMMON_CHINESE_FONTS) {
            for (String ext : SUPPORTED_FONT_EXTENSIONS) {
                String resourcePath = "fonts/" + fontName + ext;
                try {
                    URL fontUrl = getClass().getClassLoader().getResource(resourcePath);
                    if (fontUrl != null) {
                        registerFont(fontName, fontUrl.toString());
                        break; // 找到一个就跳出内层循环
                    }
                } catch (Exception e) {
                    log.debug("加载classpath字体失败: {}", resourcePath);
                }
            }
        }
    }

    /**
     * 加载系统字体（兜底方案）
     */
    private void loadSystemFonts() {
        log.debug("注册系统字体...");

        // 尝试注册系统中的中文字体
        tryRegisterSystemFont("SimSun", "宋体");
        tryRegisterSystemFont("SimHei", "黑体");
        tryRegisterSystemFont("Microsoft YaHei", "微软雅黑");
        tryRegisterSystemFont("NSimSun", "新宋体");
        tryRegisterSystemFont("FangSong", "仿宋");
        tryRegisterSystemFont("KaiTi", "楷体");

        // 注册基础拉丁字体作为最后兜底
        tryRegisterSystemFont("Arial", "Arial");
        tryRegisterSystemFont("Helvetica", "Helvetica");
        tryRegisterSystemFont("Times New Roman", "Times New Roman");

        log.debug("系统字体注册完成");
    }

    /**
     * 尝试注册系统字体
     */
    private void tryRegisterSystemFont(String fontName, String displayName) {
        try {
            // 检查字体是否已经注册
            if (registeredFonts.containsKey(fontName)) {
                return;
            }

            // 尝试通过FontFactory注册系统字体
            FontFactory.register(fontName, fontName);
            registeredFonts.put(fontName, "system:" + fontName);
            log.info("系统字体注册成功: {} ({})", fontName, displayName);
        } catch (Exception e) {
            log.debug("系统字体注册失败: {} ({})", fontName, displayName);
        }
    }

    /**
     * 注册字体文件
     */
    private void registerFontFile(Path fontFile) {
        try {
            String fontName = extractFontName(fontFile.getFileName().toString());
            String fontPath = fontFile.toAbsolutePath().toString();
            registerFont(fontName, fontPath);
        } catch (Exception e) {
            log.warn("注册字体文件失败: {}", fontFile, e);
        }
    }

    /**
     * 注册字体
     */
    private void registerFont(String fontName, String fontPath) {
        try {
            FontFactory.register(fontPath, fontName);
            registeredFonts.put(fontName, fontPath);
            log.info("字体注册成功: {} -> {}", fontName, fontPath);
        } catch (Exception e) {
            log.warn("字体注册失败: {} -> {}", fontName, fontPath, e);
        }
    }

    /**
     * 获取字体路径配置
     */
    private String getFontPath() {
        // 1. 优先使用环境变量
        String fontPath = System.getenv(FONT_PATH_ENV);
        if (!Str.isEmpty(fontPath)) {
            log.debug("使用环境变量字体路径: {}", fontPath);
            return fontPath;
        }

        // 2. 其次使用系统属性
        fontPath = System.getProperty(FONT_PATH_PROPERTY);
        if (!Str.isEmpty(fontPath)) {
            log.debug("使用系统属性字体路径: {}", fontPath);
            return fontPath;
        }

        // 3. 最后使用默认路径（如果存在）
        if (Files.exists(Paths.get(DEFAULT_EXTERNAL_FONT_DIR))) {
            log.debug("使用默认字体路径: {}", DEFAULT_EXTERNAL_FONT_DIR);
            return DEFAULT_EXTERNAL_FONT_DIR;
        }

        return null;
    }

    /**
     * 判断是否为支持的字体文件
     */
    private boolean isSupportedFontFile(Path file) {
        String fileName = file.getFileName().toString().toLowerCase();
        return SUPPORTED_FONT_EXTENSIONS.stream()
                .anyMatch(fileName::endsWith);
    }

    /**
     * 从文件名提取字体名称
     */
    private String extractFontName(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 获取已注册的字体列表
     */
    public Set<String> getRegisteredFonts() {
        return new HashSet<>(registeredFonts.keySet());
    }

    /**
     * 检查字体是否已注册
     */
    public boolean isFontRegistered(String fontName) {
        return registeredFonts.containsKey(fontName);
    }

    /**
     * 获取字体路径
     */
    public String getFontPath(String fontName) {
        return registeredFonts.get(fontName);
    }

    /**
     * 动态注册单个字体文件（线程安全）
     */
    public boolean registerFontFile(String fontFilePath) {
        if (Str.isEmpty(fontFilePath)) {
            log.warn("字体文件路径为空");
            return false;
        }

        try {
            Path fontFile = Paths.get(fontFilePath);
            if (!Files.exists(fontFile)) {
                log.warn("字体文件不存在: {}", fontFilePath);
                return false;
            }

            if (!isSupportedFontFile(fontFile)) {
                log.warn("不支持的字体文件格式: {}", fontFilePath);
                return false;
            }

            // 使用同步块确保线程安全
            synchronized (initLock) {
                String fontName = extractFontName(fontFile.getFileName().toString());

                // 检查是否已经注册
                if (registeredFonts.containsKey(fontName)) {
                    log.debug("字体已注册，跳过: {}", fontName);
                    return true;
                }

                registerFontFile(fontFile);
                return true;
            }
        } catch (Exception e) {
            log.error("动态注册字体失败: {}", fontFilePath, e);
            return false;
        }
    }

    /**
     * 清理字体缓存（主要用于测试或内存管理）
     */
    public void clearFontCache() {
        synchronized (initLock) {
            log.info("清理字体缓存");
            registeredFonts.clear();
            initialized = false;
            currentFontPath = null;
        }
    }

    /**
     * 获取字体统计信息
     */
    public Map<String, Object> getFontStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("fontCount", registeredFonts.size());
        stats.put("currentFontPath", currentFontPath);
        stats.put("registeredFonts", new HashSet<>(registeredFonts.keySet()));
        return stats;
    }
}

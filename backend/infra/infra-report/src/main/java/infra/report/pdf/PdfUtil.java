package infra.report.pdf;

import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;

/**
 * PDF工具类 - 提供简便的PDF生成方法
 */
@Slf4j
public class PdfUtil {

    /**
     * 从HTML生成PDF
     *
     * @param html HTML内容
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html) {
        return htmlToPdf(html, new PdfRenderOptions());
    }

    /**
     * 从HTML生成PDF
     *
     * @param html    HTML内容
     * @param options PDF渲染选项
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, PdfRenderOptions options) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HtmlToPdfGenerator.generatePdf(html, outputStream, options);
        return outputStream.toByteArray();
    }

    /**
     * 从HTML生成PDF到输出流
     *
     * @param html         HTML内容
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, OutputStream outputStream) {
        htmlToPdf(html, new PdfRenderOptions(), outputStream);
    }

    /**
     * 从HTML生成PDF到输出流
     *
     * @param html         HTML内容
     * @param options      PDF渲染选项
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, PdfRenderOptions options, OutputStream outputStream) {
        if (Str.isEmpty(html)) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }

        HtmlToPdfGenerator.generatePdf(html, outputStream, options);
    }
}

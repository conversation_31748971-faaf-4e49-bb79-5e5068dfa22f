package infra.report.pdf;

import infra.core.web.ServletUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.OutputStream;

/**
 * PDF工具类 - 提供简便的PDF生成方法
 */
@Slf4j
public class PdfUtil {

    /**
     * 从HTML生成PDF
     *
     * @param html HTML内容
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html) {
        return htmlToPdf(html, null, new PdfRenderOptions());
    }

    /**
     * 从HTML生成PDF（自定义选项）
     *
     * @param html    HTML内容
     * @param options PDF渲染选项
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, PdfRenderOptions options) {
        return htmlToPdf(html, null, options);
    }

    /**
     * 从HTML生成PDF（自定义CSS）
     *
     * @param html HTML内容
     * @param css  CSS内容
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, String css) {
        return htmlToPdf(html, css, new PdfRenderOptions());
    }

    /**
     * 从HTML生成PDF（指定baseUrl用于解析相对路径）
     *
     * @param html    HTML内容
     * @param baseUrl 基础URL，用于解析相对路径的图片和CSS
     * @return PDF字节数组
     */
    public static byte[] htmlToPdfWithBaseUrl(String html, String baseUrl) {
        PdfRenderOptions options = PdfRenderOptions.builder()
                .baseUrl(baseUrl)
                .build();
        return htmlToPdf(html, null, options);
    }

    /**
     * 从HTML生成PDF（指定baseUrl和CSS）
     *
     * @param html    HTML内容
     * @param css     CSS内容
     * @param baseUrl 基础URL，用于解析相对路径的图片和CSS
     * @return PDF字节数组
     */
    public static byte[] htmlToPdfWithBaseUrl(String html, String css, String baseUrl) {
        PdfRenderOptions options = PdfRenderOptions.builder()
                .baseUrl(baseUrl)
                .build();
        return htmlToPdf(html, css, options);
    }

    /**
     * 从HTML生成PDF（自定义选项和CSS）
     *
     * @param html    HTML内容
     * @param css     CSS内容
     * @param options PDF渲染选项
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, String css, PdfRenderOptions options) {
        // 自动设置baseUrl
        PdfRenderOptions finalOptions = ensureBaseUrl(options);

        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(finalOptions)) {
            return generator.generatePdf(html, css);
        } catch (Exception e) {
            log.error("HTML转PDF失败", e);
            throw new RuntimeException("HTML转PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从HTML生成PDF到输出流
     *
     * @param html         HTML内容
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, OutputStream outputStream) {
        htmlToPdf(html, null, new PdfRenderOptions(), outputStream);
    }

    /**
     * 从HTML生成PDF到输出流（自定义选项）
     *
     * @param html         HTML内容
     * @param options      PDF渲染选项
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, PdfRenderOptions options, OutputStream outputStream) {
        htmlToPdf(html, null, options, outputStream);
    }

    /**
     * 从HTML生成PDF到输出流（自定义CSS）
     *
     * @param html         HTML内容
     * @param css          CSS内容
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, String css, OutputStream outputStream) {
        htmlToPdf(html, css, new PdfRenderOptions(), outputStream);
    }

    /**
     * 从HTML生成PDF到输出流（自定义选项和CSS）
     *
     * @param html         HTML内容
     * @param css          CSS内容
     * @param options      PDF渲染选项
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, String css, PdfRenderOptions options, OutputStream outputStream) {
        if (StringUtils.isBlank(html)) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }

        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
            generator.generatePdf(html, css, outputStream);
        } catch (Exception e) {
            log.error("HTML转PDF失败", e);
            throw new RuntimeException("HTML转PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确保PdfRenderOptions中有baseUrl
     * 如果没有设置baseUrl，则自动从当前请求中获取
     */
    private static PdfRenderOptions ensureBaseUrl(PdfRenderOptions options) {
        if (options == null) {
            options = new PdfRenderOptions();
        }

        // 如果已经设置了baseUrl，直接返回
        if (StringUtils.isNotBlank(options.getBaseUrl())) {
            return options;
        }

        // 尝试从当前请求中获取baseUrl
        try {
            HttpServletRequest request = ServletUtil.getRequest();
            if (request != null) {
                String baseUrl = buildBaseUrl(request);
                log.debug("自动设置baseUrl: {}", baseUrl);

                // 创建新的options对象，设置baseUrl
                return PdfRenderOptions.builder()
                        .marginLeft(options.getMarginLeft())
                        .marginRight(options.getMarginRight())
                        .marginTop(options.getMarginTop())
                        .marginBottom(options.getMarginBottom())
                        .defaultFontFamily(options.getDefaultFontFamily())
                        .fontPath(options.getFontPath())
                        .defaultFontSize(options.getDefaultFontSize())
                        .watermarkOptions(options.getWatermarkOptions())
                        .headerFooterOptions(options.getHeaderFooterOptions())
                        .customCssProperties(options.getCustomCssProperties())
                        .externalFontPath(options.getExternalFontPath())
                        .baseUrl(baseUrl)
                        .build();
            }
        } catch (Exception e) {
            log.debug("无法获取当前请求，跳过自动设置baseUrl", e);
        }

        return options;
    }

    /**
     * 构建baseUrl
     * 支持相对路径（基于当前请求路径）和绝对路径（基于站点根目录）
     */
    private static String buildBaseUrl(HttpServletRequest request) {
        StringBuilder baseUrl = new StringBuilder();

        // 协议
        baseUrl.append(request.getScheme()).append("://");

        // 主机和端口
        baseUrl.append(request.getServerName());
        int port = request.getServerPort();
        if (port != 80 && port != 443) {
            baseUrl.append(":").append(port);
        }

        // 上下文路径（用于绝对路径）
        String contextPath = request.getContextPath();
        if (StringUtils.isNotBlank(contextPath)) {
            baseUrl.append(contextPath);
        }

        return baseUrl.toString();
    }
}

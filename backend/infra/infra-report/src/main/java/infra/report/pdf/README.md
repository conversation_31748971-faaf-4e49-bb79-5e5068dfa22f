# HTML转PDF工具类使用说明

基于OpenPDF 2.2.4和openpdf-html的现代化HTML转PDF解决方案。

## 特性

- ✅ 基于最新OpenPDF 2.2.4 API
- ✅ 支持现代CSS样式
- ✅ 完整的中文字体支持
- ✅ CSS水印（文本和图片）
- ✅ CSS页眉页脚
- ✅ 外部资源支持（图片、CSS）
- ✅ 多种图片来源（classpath、static、远程URL）
- ✅ 现代Java 24特性
- ✅ 优雅的API设计

## 快速开始

### 基础用法

```java
// 最简单的用法
String html = "<h1>Hello World</h1><p>这是中文内容</p>";
byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html);
```

### 带自定义CSS

```java
String html = "<h1 class='title'>标题</h1>";
String css = ".title { color: red; text-align: center; }";
byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, css);
```

### 完整配置

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .marginTop(50f)
    .marginBottom(50f)
    .marginLeft(40f)
    .marginRight(40f)
    .defaultFontFamily("SimHei")
    .defaultFontSize(12f)
    .baseUrl("https://example.com")
    .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
        .text("机密文档")
        .color("#FF0000")
        .opacity(0.3f)
        .rotation(45f)
        .position("center")
        .build())
    .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
        .headerCenter("公司文档")
        .footerLeft("机密")
        .showPageNumbers(true)
        .build())
    .build();

byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, css, options);
```

## 水印配置

### 文本水印

```java
WatermarkOptions watermark = WatermarkOptions.builder()
    .text("机密文档")
    .color("#CCCCCC")          // 颜色
    .opacity(0.3f)             // 透明度
    .rotation(45f)             // 旋转角度
    .fontSize(48f)             // 字体大小
    .position("center")        // 位置
    .build();
```

### 图片水印

```java
WatermarkOptions watermark = WatermarkOptions.builder()
    .imagePath("images/logo.png")  // 支持classpath路径
    // .imagePath("/static/logo.png")  // 支持static路径
    // .imagePath("https://example.com/logo.png")  // 支持远程URL
    .opacity(0.5f)
    .position("top-right")
    .build();
```

### 支持的水印位置

- `center` - 居中（默认）
- `top-left` - 左上角
- `top-center` - 上方居中
- `top-right` - 右上角
- `center-left` - 左侧居中
- `center-right` - 右侧居中
- `bottom-left` - 左下角
- `bottom-center` - 下方居中
- `bottom-right` - 右下角

## 页眉页脚配置

```java
HeaderFooterOptions headerFooter = HeaderFooterOptions.builder()
    .headerLeft("左侧页眉")
    .headerCenter("中间页眉")
    .headerRight("右侧页眉")
    .footerLeft("左侧页脚")
    .footerCenter("中间页脚")
    .footerRight("右侧页脚")
    .showPageNumbers(true)
    .pageNumberFormat("第 {page} 页，共 {total} 页")
    .fontSize(10f)
    .build();
```

## 字体支持

### 自动注册的中文字体

系统会自动尝试注册以下字体（如果存在）：

- `fonts/SimHei.ttf` - 黑体
- `fonts/SimSun.ttf` - 宋体
- `fonts/NotoSansCJK-Regular.ttc` - Google Noto Sans CJK
- `fonts/SourceHanSans-Regular.ttc` - Adobe Source Han Sans
- `fonts/PingFang.ttc` - 苹果苹方
- `fonts/MicrosoftYaHei.ttf` - 微软雅黑
- `fonts/DroidSansFallback.ttf` - Android默认中文字体

### 在CSS中使用字体

```css
body {
    font-family: 'SimHei', 'Microsoft YaHei', sans-serif;
}
```

## 外部资源支持

### 图片资源

```html
<!-- classpath资源 -->
<img src="images/logo.png" />

<!-- static资源 -->
<img src="/static/images/banner.jpg" />

<!-- 远程资源（需要设置baseUrl） -->
<img src="https://example.com/image.png" />

<!-- 相对路径（需要设置baseUrl） -->
<img src="./assets/photo.jpg" />
```

### CSS资源

```html
<!-- 外部CSS -->
<link rel="stylesheet" href="https://example.com/styles.css" />

<!-- 相对路径CSS -->
<link rel="stylesheet" href="./css/custom.css" />
```

## 最佳实践

### 1. 使用完整的HTML文档结构

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>文档标题</title>
    <style>
        /* 内联样式 */
    </style>
</head>
<body>
    <!-- 内容 -->
</body>
</html>
```

### 2. 优化CSS for PDF

```css
/* 使用@page规则控制页面 */
@page {
    size: A4;
    margin: 2cm;
}

/* 避免分页断裂 */
.no-break {
    page-break-inside: avoid;
}

/* 强制分页 */
.page-break {
    page-break-before: always;
}
```

### 3. 资源管理

- 将字体文件放在 `src/main/resources/fonts/` 目录
- 将图片资源放在 `src/main/resources/static/images/` 目录
- 设置合适的 `baseUrl` 用于解析相对路径

### 4. 异常处理

```java
try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
    return generator.generatePdf(html, css);
} catch (PdfException e) {
    log.error("PDF生成失败", e);
    // 处理异常
}
```

## 注意事项

1. **字体文件**: 确保所需的中文字体文件存在于resources目录
2. **CSS兼容性**: 使用PDF兼容的CSS属性
3. **图片格式**: 支持常见的图片格式（PNG、JPG、GIF等）
4. **内存使用**: 大文档可能消耗较多内存
5. **版权合规**: 确保使用的字体文件符合版权要求

## 故障排除

### 中文显示问题
- 检查字体文件是否存在
- 确认CSS中指定了正确的字体族

### 图片不显示
- 检查图片路径是否正确
- 确认baseUrl设置是否正确
- 验证图片文件是否可访问

### 样式不生效
- 检查CSS语法是否正确
- 确认使用的是PDF兼容的CSS属性
- 验证CSS选择器是否正确

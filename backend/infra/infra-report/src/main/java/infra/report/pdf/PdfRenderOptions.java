package infra.report.pdf;

import com.lowagie.text.PageSize;
import com.lowagie.text.Rectangle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * PDF 渲染选项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PdfRenderOptions implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 页面尺寸
     */
    private Rectangle pageSize = PageSize.A4;

    /**
     * 页边距
     */
    private float marginLeft = 36f;
    private float marginRight = 36f;
    private float marginTop = 72f;
    private float marginBottom = 72f;

    /**
     * 字体配置
     */
    private String defaultFontFamily = "SimHei";
    private String fontPath = "fonts/SimHei.ttf";
    private float defaultFontSize = 12f;
    private String fontEncoding = "UniGB-UCS2-H";

    /**
     * 水印设置
     */
    private WatermarkOptions watermarkOptions;

    /**
     * 页眉页脚配置
     */
    private HeaderFooterOptions headerFooterOptions;

    /**
     * 文档元数据
     */
    private String title;
    private String author;
    private String subject;

    /**
     * 渲染配置
     */
    private boolean enableJavaScript = false;
    private int timeoutSeconds = 120;
    private Map<String, String> customCssProperties = new HashMap<>();

    /**
     * 资源配置
     */
    private String baseUrl;
    private boolean enableResourceCache = true;
    private String cacheDirectory = "pdf-cache";

    /**
     * 水印配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class WatermarkOptions {
        /**
         * 文本水印
         */
        private String text;
        /**
         * 文本水印颜色(默认#CCCCCC)
         */
        private String color = "#CCCCCC";
        /**
         * 图片水印路径
         */
        private String imagePath;
        /**
         * 透明度(默认0.3)
         */
        private float opacity = 0.3f;
        /**
         * 旋转(默认45f)
         */
        private float rotation = 45f;
        /**
         * 水印字体大小(默认48f)
         */
        private float fontSize = 48f;
        /**
         * 水印位置，center, top-left, top-right, bottom-left, bottom-right(默认为center)
         */
        private String position = "center";
    }

    /**
     * 页眉页脚配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class HeaderFooterOptions {
        /**
         * 页眉左侧字符
         */
        private String headerLeft;
        /**
         * 页眉中间字符
         */
        private String headerCenter;
        /**
         * 页眉右侧字符
         */
        private String headerRight;
        /**
         * 页角左侧字符
         */
        private String footerLeft;
        /**
         * 页角中间字符
         */
        private String footerCenter;
        /**
         * 页角右侧字符
         */
        private String footerRight;
        /**
         * 是否显示页码
         */
        private boolean showPageNumbers = true;
        /**
         * 页码格式(默认第 {page} 页， 共 {total} 页)
         */
        private String pageNumberFormat = "第 {page} 页， 共 {total} 页";
        /**
         * 字体大小(默认10f)
         */
        private float fontSize = 10f;
    }
}

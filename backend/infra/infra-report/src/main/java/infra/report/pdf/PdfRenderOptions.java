package infra.report.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * PDF 渲染选项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PdfRenderOptions implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 页边距（点为单位）
     */
    @Builder.Default
    private float marginLeft = 36f;
    @Builder.Default
    private float marginRight = 36f;
    @Builder.Default
    private float marginTop = 72f;
    @Builder.Default
    private float marginBottom = 72f;

    /**
     * 字体配置
     */
    @Builder.Default
    private String defaultFontFamily = "SimHei";
    @Builder.Default
    private String fontPath = "fonts/SimHei.ttf";
    @Builder.Default
    private float defaultFontSize = 12f;

    /**
     * 水印设置
     */
    private WatermarkOptions watermarkOptions;

    /**
     * 页眉页脚配置
     */
    private HeaderFooterOptions headerFooterOptions;

    /**
     * 文档元数据
     */
    private String title;
    private String author;
    private String subject;

    /**
     * 自定义CSS属性
     */
    @Builder.Default
    private Map<String, String> customCssProperties = new HashMap<>();

    /**
     * 基础URL（用于解析相对路径的外部资源）
     */
    private String baseUrl;

    /**
     * 水印配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class WatermarkOptions {
        /**
         * 文本水印
         */
        private String text;
        /**
         * 文本水印颜色(默认#CCCCCC)
         */
        @Builder.Default
        private String color = "#CCCCCC";
        /**
         * 图片水印路径
         */
        private String imagePath;
        /**
         * 透明度(默认0.3)
         */
        @Builder.Default
        private float opacity = 0.3f;
        /**
         * 旋转(默认45f)
         */
        @Builder.Default
        private float rotation = 45f;
        /**
         * 水印字体大小(默认48f)
         */
        @Builder.Default
        private float fontSize = 48f;
        /**
         * 水印位置，center, top-left, top-right, bottom-left, bottom-right(默认为center)
         */
        @Builder.Default
        private String position = "center";
    }

    /**
     * 页眉页脚配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class HeaderFooterOptions {
        /**
         * 页眉左侧字符
         */
        private String headerLeft;
        /**
         * 页眉中间字符
         */
        private String headerCenter;
        /**
         * 页眉右侧字符
         */
        private String headerRight;
        /**
         * 页角左侧字符
         */
        private String footerLeft;
        /**
         * 页角中间字符
         */
        private String footerCenter;
        /**
         * 页角右侧字符
         */
        private String footerRight;
        /**
         * 是否显示页码
         */
        @Builder.Default
        private boolean showPageNumbers = true;
        /**
         * 页码格式(默认第 {page} 页， 共 {total} 页)
         */
        @Builder.Default
        private String pageNumberFormat = "第 {page} 页， 共 {total} 页";
        /**
         * 字体大小(默认10f)
         */
        @Builder.Default
        private float fontSize = 10f;
    }
}

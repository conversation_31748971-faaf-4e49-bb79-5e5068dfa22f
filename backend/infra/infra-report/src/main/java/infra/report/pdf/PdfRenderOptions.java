package infra.report.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * PDF 渲染选项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class PdfRenderOptions implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // 默认页边距常量
    public static final float DEFAULT_MARGIN = 36f;
    public static final float DEFAULT_TOP_BOTTOM_MARGIN = 72f;
    public static final float DEFAULT_FONT_SIZE = 12f;

    /**
     * 页边距（点为单位）
     */
    @Builder.Default
    private float marginLeft = DEFAULT_MARGIN;
    @Builder.Default
    private float marginRight = DEFAULT_MARGIN;
    @Builder.Default
    private float marginTop = DEFAULT_TOP_BOTTOM_MARGIN;
    @Builder.Default
    private float marginBottom = DEFAULT_TOP_BOTTOM_MARGIN;

    /**
     * 字体配置
     */
    @Builder.Default
    private String defaultFontFamily = "simhei";
    private String fontPath;
    @Builder.Default
    private float defaultFontSize = 12f;

    /**
     * 水印设置
     */
    private WatermarkOptions watermarkOptions;

    /**
     * 页眉页脚配置
     */
    private HeaderFooterOptions headerFooterOptions;

    /**
     * 自定义CSS属性
     */
    @Builder.Default
    private Map<String, String> customCssProperties = new HashMap<>();

    /**
     * 基础URL（用于解析相对路径的外部资源）
     */
    private String baseUrl;

    /**
     * 纸张大小（默认A4）
     */
    @Builder.Default
    private PageSize pageSize = PageSize.A4;

    /**
     * 水印配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class WatermarkOptions {
        /**
         * 文本水印
         */
        private String text;
        /**
         * 文本水印颜色(默认#CCCCCC)
         */
        @Builder.Default
        private String color = "#CCCCCC";
        /**
         * 图片水印路径
         */
        private String imagePath;
        /**
         * 透明度(默认0.3)
         */
        @Builder.Default
        private float opacity = 0.3f;
        /**
         * 旋转(默认45f)
         */
        @Builder.Default
        private float rotation = 45f;
        /**
         * 水印字体大小(默认48f)
         */
        @Builder.Default
        private float fontSize = 48f;
        /**
         * 水印位置，center, top-left, top-right, bottom-left, bottom-right(默认为center)
         */
        @Builder.Default
        private String position = "center";

        /**
         * 是否重复水印覆盖整个页面(默认false)
         */
        @Builder.Default
        private boolean repeatWatermark = false;

        /**
         * 水平间距，仅在repeatWatermark=true时有效(默认200f)
         */
        @Builder.Default
        private float horizontalSpacing = 200f;

        /**
         * 垂直间距，仅在repeatWatermark=true时有效(默认150f)
         */
        @Builder.Default
        private float verticalSpacing = 150f;

        /**
         * 稀疏度：1.0=正常密度，0.5=稀疏一半，2.0=密集一倍(默认1.0f)
         */
        @Builder.Default
        private float density = 1.0f;
    }

    /**
     * 页眉页脚配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class HeaderFooterOptions {
        /**
         * 页眉左侧字符
         */
        private String headerLeft;
        /**
         * 页眉左侧颜色
         */
        @Builder.Default
        private String headerLeftColor = "#000000";
        /**
         * 页眉中间字符
         */
        private String headerCenter;
        /**
         * 页眉中间颜色
         */
        @Builder.Default
        private String headerCenterColor = "#000000";
        /**
         * 页眉右侧字符
         */
        private String headerRight;
        /**
         * 页眉右侧颜色
         */
        @Builder.Default
        private String headerRightColor = "#000000";
        /**
         * 页角左侧字符
         */
        private String footerLeft;
        /**
         * 页角左侧颜色
         */
        @Builder.Default
        private String footerLeftColor = "#000000";
        /**
         * 页角中间字符
         */
        private String footerCenter;
        /**
         * 页角中间颜色
         */
        @Builder.Default
        private String footerCenterColor = "#000000";
        /**
         * 页角右侧字符
         */
        private String footerRight;
        /**
         * 页角右侧颜色
         */
        @Builder.Default
        private String footerRightColor = "#000000";
        /**
         * 是否显示页码
         */
        @Builder.Default
        private boolean showPageNumbers = true;
        /**
         * 页码格式(默认第 {page} 页， 共 {total} 页)
         */
        @Builder.Default
        private String pageNumberFormat = "第 {page} 页， 共 {total} 页";
        /**
         * 字体大小(默认10f)
         */
        @Builder.Default
        private float fontSize = 10f;
    }

    /**
     * 页面大小枚举
     */
    public enum PageSize {
        A4, A3, A5, LETTER, LEGAL
    }
}

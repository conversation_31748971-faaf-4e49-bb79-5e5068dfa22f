package infra.report.pdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * PDF 渲染选项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PdfRenderOptions implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 页边距（点为单位）
     */
    @Builder.Default
    private float marginLeft = 36f;
    @Builder.Default
    private float marginRight = 36f;
    @Builder.Default
    private float marginTop = 72f;
    @Builder.Default
    private float marginBottom = 72f;

    /**
     * 字体配置
     */
    @Builder.Default
    private String defaultFontFamily = "SimHei";
    private String fontPath;
    @Builder.Default
    private float defaultFontSize = 12f;
    /**
     * 外部字体目录路径（可选，用于覆盖默认的字体加载路径）
     * 如果不设置，将按以下优先级查找字体：
     * 1. 环境变量 FONT_PATH
     * 2. 系统属性 font.path
     * 3. 默认路径 /app/fonts
     */
    private String externalFontPath;

    /**
     * 水印设置
     */
    private WatermarkOptions watermarkOptions;

    /**
     * 页眉页脚配置
     */
    private HeaderFooterOptions headerFooterOptions;

    /**
     * 自定义CSS属性
     */
    @Builder.Default
    private Map<String, String> customCssProperties = new HashMap<>();

    /**
     * 基础URL（用于解析相对路径的外部资源）
     */
    private String baseUrl;

    /**
     * 纸张大小（默认A4）
     */
    @Builder.Default
    private PageSize pageSize = PageSize.A4;

    /**
     * 水印配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class WatermarkOptions {
        /**
         * 文本水印
         */
        private String text;
        /**
         * 文本水印颜色(默认#CCCCCC)
         */
        @Builder.Default
        private String color = "#CCCCCC";
        /**
         * 图片水印路径
         */
        private String imagePath;
        /**
         * 透明度(默认0.3)
         */
        @Builder.Default
        private float opacity = 0.3f;
        /**
         * 旋转(默认45f)
         */
        @Builder.Default
        private float rotation = 45f;
        /**
         * 水印字体大小(默认48f)
         */
        @Builder.Default
        private float fontSize = 48f;
        /**
         * 水印位置，center, top-left, top-right, bottom-left, bottom-right(默认为center)
         */
        @Builder.Default
        private String position = "center";

        /**
         * 是否重复水印覆盖整个页面(默认false)
         */
        @Builder.Default
        private boolean repeatWatermark = false;

        /**
         * 水平间距，仅在repeatWatermark=true时有效(默认200f)
         */
        @Builder.Default
        private float horizontalSpacing = 200f;

        /**
         * 垂直间距，仅在repeatWatermark=true时有效(默认150f)
         */
        @Builder.Default
        private float verticalSpacing = 150f;

        /**
         * 稀疏度：1.0=正常密度，0.5=稀疏一半，2.0=密集一倍(默认1.0f)
         */
        @Builder.Default
        private float density = 1.0f;
    }

    /**
     * 页眉页脚配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class HeaderFooterOptions {
        /**
         * 页眉左侧字符
         */
        private String headerLeft;
        /**
         * 页眉中间字符
         */
        private String headerCenter;
        /**
         * 页眉右侧字符
         */
        private String headerRight;
        /**
         * 页角左侧字符
         */
        private String footerLeft;
        /**
         * 页角中间字符
         */
        private String footerCenter;
        /**
         * 页角右侧字符
         */
        private String footerRight;
        /**
         * 是否显示页码
         */
        @Builder.Default
        private boolean showPageNumbers = true;
        /**
         * 页码格式(默认第 {page} 页， 共 {total} 页)
         */
        @Builder.Default
        private String pageNumberFormat = "第 {page} 页， 共 {total} 页";
        /**
         * 字体大小(默认10f)
         */
        @Builder.Default
        private float fontSize = 10f;
    }
}

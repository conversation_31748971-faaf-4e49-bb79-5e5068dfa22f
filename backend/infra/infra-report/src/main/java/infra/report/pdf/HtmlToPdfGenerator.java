package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.openpdf.pdf.ITextRenderer;

import java.awt.Color;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * HTML转PDF生成器
 * 支持水印、页眉页脚、外部资源
 */
@Slf4j
public class HtmlToPdfGenerator {

    // 字体加载缓存，避免重复加载
    private static volatile boolean systemFontsLoaded = false;
    private static final Map<String, Boolean> customFontDirCache = new ConcurrentHashMap<>();

    /**
     * 将HTML转换为PDF
     */
    public static void generatePdf(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        if (Str.isEmpty(htmlContent)) {
            throw new PdfException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new PdfException("输出流不能为空");
        }

        if (options == null) {
            options = new PdfRenderOptions();
        }

        // 自动设置baseUrl
        if (Str.isEmpty(options.getBaseUrl())) {
            ensureBaseUrl(options);
        }

        try {
            // 如果需要水印或页眉页脚，使用传统PDF方式
            if (hasWatermarkOrHeaderFooter(options)) {
                generatePdfWithEvents(htmlContent, outputStream, options);
            } else {
                // 纯HTML转PDF，使用HTML渲染器
                generatePdfWithRenderer(htmlContent, outputStream, options);
            }
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new PdfException("生成PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从文件生成PDF
     */
    public static void generatePdfFromFile(String htmlFilePath, String outputFilePath, PdfRenderOptions options) {
        try {
            Path path = Paths.get(htmlFilePath);
            String htmlContent = Files.readString(path);

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                String baseUrl = path.getParent().toUri().toString();
                options.setBaseUrl(baseUrl);
            }

            try (FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {
                generatePdf(htmlContent, outputStream, options);
            }

        } catch (Exception e) {
            throw new PdfException("从文件生成PDF失败", e);
        }
    }

    /**
     * 生成PDF到字节数组
     */
    public static byte[] generatePdfToBytes(String htmlContent, PdfRenderOptions options) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generatePdf(htmlContent, outputStream, options);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new PdfException("生成PDF字节数组失败", e);
        }
    }

    /**
     * 生成PDF到字节数组（从文件）
     */
    public static byte[] generatePdfFromFileToBytes(String htmlFilePath, PdfRenderOptions options) {
        try {
            Path path = Paths.get(htmlFilePath);
            String htmlContent = Files.readString(path);

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                String baseUrl = path.getParent().toUri().toString();
                options.setBaseUrl(baseUrl);
            }

            return generatePdfToBytes(htmlContent, options);
        } catch (Exception e) {
            throw new PdfException("从文件生成PDF字节数组失败", e);
        }
    }

    /**
     * 预处理HTML，注入页边距和自定义CSS
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        StringBuilder cssBuilder = new StringBuilder();

        // 添加页面设置CSS
        cssBuilder.append("@page { ").append("margin-top: ").append(options.getMarginTop()).append("pt; ").append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ").append("margin-left: ").append(options.getMarginLeft()).append("pt; ").append("margin-right: ").append(options.getMarginRight()).append("pt; ");

        // 添加页面大小设置
        if (options.getPageSize() != null) {
            cssBuilder.append("size: ").append(getPageSizeCss(options.getPageSize())).append("; ");
        }

        cssBuilder.append("} ");

        // 添加默认字体CSS
        cssBuilder.append("body { ").append("font-family: '").append(options.getDefaultFontFamily()).append("', sans-serif; ").append("font-size: ").append(options.getDefaultFontSize()).append("pt; ").append("} ");

        // 添加自定义CSS属性
        if (options.getCustomCssProperties() != null) {
            options.getCustomCssProperties().forEach((selector, rules) -> cssBuilder.append(selector).append(" { ").append(rules).append(" } "));
        }

        String cssContent = cssBuilder.toString();

        // 如果HTML中没有head标签，添加一个
        if (!htmlContent.toLowerCase().contains("<head>")) {
            if (htmlContent.toLowerCase().contains("<html>")) {
                htmlContent = htmlContent.replaceFirst("(?i)<html[^>]*>", "$0<head><style>" + cssContent + "</style></head>");
            } else {
                htmlContent = "<html><head><style>" + cssContent + "</style></head><body>" + htmlContent + "</body></html>";
            }
        } else {
            // 在head标签中添加CSS
            htmlContent = htmlContent.replaceFirst("(?i)</head>", "<style>" + cssContent + "</style></head>");
        }

        return htmlContent;
    }

    /**
     * 自动设置baseUrl
     */
    private static PdfRenderOptions ensureBaseUrl(PdfRenderOptions options) {
        try {
            var request = ServletUtil.getRequest();
            if (request != null) {
                String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
                options.setBaseUrl(baseUrl);
            }
        } catch (Exception e) {
            log.debug("无法获取当前请求，跳过自动设置baseUrl", e);
        }

        return options;
    }

    /**
     * 检查是否需要水印或页眉页脚
     */
    private static boolean hasWatermarkOrHeaderFooter(PdfRenderOptions options) {
        return (options.getWatermarkOptions() != null && (!Str.isEmpty(options.getWatermarkOptions().getText()) || !Str.isEmpty(options.getWatermarkOptions().getImagePath()))) || (options.getHeaderFooterOptions() != null);
    }

    /**
     * 使用事件处理器生成PDF（支持水印和页眉页脚）
     */
    private static void generatePdfWithEvents(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        ByteArrayOutputStream tempStream = null;
        ByteArrayInputStream inputStream = null;
        PdfReader reader = null;
        PdfStamper stamper = null;

        try {
            // 先生成基础HTML到PDF
            tempStream = new ByteArrayOutputStream();
            generatePdfWithRenderer(htmlContent, tempStream, options);

            // 读取生成的PDF并添加水印和页眉页脚
            byte[] pdfBytes = tempStream.toByteArray();
            inputStream = new ByteArrayInputStream(pdfBytes);

            reader = new PdfReader(inputStream);
            stamper = new PdfStamper(reader, outputStream);

            // 添加水印和页眉页脚
            addWatermarkAndHeaderFooter(stamper, options);

        } catch (Exception e) {
            throw new PdfException("生成带事件PDF失败", e);
        } finally {
            // 确保资源清理
            closeQuietly(stamper);
            closeQuietly(reader);
            closeQuietly(inputStream);
            closeQuietly(tempStream);
        }
    }

    /**
     * 使用HTML渲染器生成PDF（纯HTML转换）
     */
    private static void generatePdfWithRenderer(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        ITextRenderer renderer = null;
        try {
            // HTML渲染器的页面大小主要通过CSS @page规则控制
            renderer = new ITextRenderer();

            // 设置渲染选项
            renderer.getSharedContext().setPrint(true);
            renderer.getSharedContext().setInteractive(false);

            // 设置字体
            setupFonts(renderer, options);

            // 处理HTML内容
            String processedHtml = preprocessHtml(htmlContent, options);

            // 设置文档
            if (!Str.isEmpty(options.getBaseUrl())) {
                renderer.setDocumentFromString(processedHtml, options.getBaseUrl());
            } else {
                renderer.setDocumentFromString(processedHtml);
            }

            renderer.layout();
            renderer.createPDF(outputStream);

        } catch (Exception e) {
            throw new PdfException("HTML-PDF渲染失败", e);
        } finally {
            // 确保资源清理
            if (renderer != null) {
                try {
                    renderer.finishPDF();
                } catch (Exception e) {
                    log.debug("清理HTML渲染器资源时出现异常", e);
                }
            }
        }
    }

    /**
     * 添加水印和页眉页脚
     */
    private static void addWatermarkAndHeaderFooter(PdfStamper stamper, PdfRenderOptions options) {
        try {
            int totalPages = stamper.getReader().getNumberOfPages();

            for (int i = 1; i <= totalPages; i++) {
                PdfContentByte canvas = stamper.getOverContent(i);
                Rectangle pageSize = stamper.getReader().getPageSize(i);

                // 添加水印
                if (options.getWatermarkOptions() != null) {
                    WatermarkProcessor.addWatermark(canvas, pageSize, options.getWatermarkOptions());
                }

                // 添加页眉页脚
                if (options.getHeaderFooterOptions() != null) {
                    addHeaderFooter(canvas, pageSize, options.getHeaderFooterOptions(), i, totalPages);
                }
            }
        } catch (Exception e) {
            log.error("添加水印和页眉页脚失败", e);
        }
    }

    /**
     * 添加页眉页脚
     */
    private static void addHeaderFooter(PdfContentByte canvas, Rectangle pageSize, PdfRenderOptions.HeaderFooterOptions options, int currentPage, int totalPages) {
        try {
            // 创建默认字体
            BaseFont baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            Font font = new Font(baseFont, options.getFontSize());

            float headerY = pageSize.getTop() - 20;
            float footerY = pageSize.getBottom() + 20;

            // 页眉
            if (!Str.isEmpty(options.getHeaderLeft())) {
                addColoredText(canvas, options.getHeaderLeft(), 50, headerY, Element.ALIGN_LEFT, font, options.getHeaderLeftColor());
            }
            if (!Str.isEmpty(options.getHeaderCenter())) {
                addColoredText(canvas, options.getHeaderCenter(), pageSize.getWidth() / 2, headerY, Element.ALIGN_CENTER, font, options.getHeaderCenterColor());
            }
            if (!Str.isEmpty(options.getHeaderRight())) {
                addColoredText(canvas, options.getHeaderRight(), pageSize.getWidth() - 50, headerY, Element.ALIGN_RIGHT, font, options.getHeaderRightColor());
            }

            // 页脚
            if (!Str.isEmpty(options.getFooterLeft())) {
                String text = processPageNumberTemplate(options.getFooterLeft(), currentPage, totalPages);
                addColoredText(canvas, text, 50, footerY, Element.ALIGN_LEFT, font, options.getFooterLeftColor());
            }
            if (!Str.isEmpty(options.getFooterCenter())) {
                String text = processPageNumberTemplate(options.getFooterCenter(), currentPage, totalPages);
                addColoredText(canvas, text, pageSize.getWidth() / 2, footerY, Element.ALIGN_CENTER, font, options.getFooterCenterColor());
            }
            if (!Str.isEmpty(options.getFooterRight())) {
                String text = processPageNumberTemplate(options.getFooterRight(), currentPage, totalPages);
                addColoredText(canvas, text, pageSize.getWidth() - 50, footerY, Element.ALIGN_RIGHT, font, options.getFooterRightColor());
            }

            // 默认页码
            if (options.isShowPageNumbers() && Str.isEmpty(options.getFooterLeft()) && Str.isEmpty(options.getFooterCenter()) && Str.isEmpty(options.getFooterRight())) {
                String pageText = processPageNumberTemplate(options.getPageNumberFormat(), currentPage, totalPages);
                addColoredText(canvas, pageText, pageSize.getWidth() / 2, footerY, Element.ALIGN_CENTER, font, "#000000");
            }

        } catch (Exception e) {
            log.error("添加页眉页脚失败", e);
        }
    }

    /**
     * 添加带颜色的文本
     */
    private static void addColoredText(PdfContentByte canvas, String text, float x, float y, int alignment, Font font, String color) {
        try {
            // 解析颜色
            Color textColor = parseColor(color);

            // 创建带颜色的字体
            Font coloredFont = new Font(font.getBaseFont(), font.getSize(), font.getStyle(), textColor);

            Phrase phrase = new Phrase(text, coloredFont);
            ColumnText.showTextAligned(canvas, alignment, phrase, x, y, 0);
        } catch (Exception e) {
            log.error("添加带颜色文本失败", e);
        }
    }

    /**
     * 解析颜色字符串
     */
    private static Color parseColor(String colorStr) {
        try {
            if (Str.isEmpty(colorStr)) {
                return Color.BLACK;
            }

            if (colorStr.startsWith("#")) {
                colorStr = colorStr.substring(1);
            }

            if (colorStr.length() == 6) {
                int r = Integer.parseInt(colorStr.substring(0, 2), 16);
                int g = Integer.parseInt(colorStr.substring(2, 4), 16);
                int b = Integer.parseInt(colorStr.substring(4, 6), 16);
                return new Color(r, g, b);
            }

            return Color.BLACK;
        } catch (Exception e) {
            log.warn("解析颜色失败: {}，将采用默认黑色", colorStr);
            return Color.BLACK;
        }
    }

    /**
     * 获取页面大小的CSS值
     */
    private static String getPageSizeCss(PageSize pageSize) {
        return switch (pageSize) {
            case A3 -> "A3";
            case A5 -> "A5";
            case LETTER -> "letter";
            case LEGAL -> "legal";
            default -> "A4";
        };
    }

    /**
     * 处理页码模板
     */
    private static String processPageNumberTemplate(String template, int currentPage, int totalPages) {
        if (Str.isEmpty(template)) {
            return "";
        }
        return template.replace("{page}", String.valueOf(currentPage)).replace("{total}", String.valueOf(totalPages));
    }

    /**
     * 设置字体
     */
    private static void setupFonts(ITextRenderer renderer, PdfRenderOptions options) {
        try {
            String customFontPath = determineFontPath(options);

            // 如果有自定义字体路径，加载自定义字体
            if (!Str.isEmpty(customFontPath)) {
                loadCustomFonts(renderer, customFontPath);
            }

            // 加载系统字体
            loadSystemFonts(renderer);

        } catch (Exception e) {
            log.warn("设置字体失败", e);
        }
    }

    /**
     * 加载自定义字体目录(不抛异常，只记录log)
     */
    private static void loadCustomFonts(ITextRenderer renderer, String fontPath) {
        // 检查缓存，避免重复加载
        if (customFontDirCache.containsKey(fontPath)) {
            log.debug("自定义字体目录已缓存: {}", fontPath);
            return;
        }

        try {
            Path fontDir = Paths.get(fontPath);
            if (!Files.exists(fontDir) || !Files.isDirectory(fontDir)) {
                log.warn("自定义字体目录不存在: {}", fontPath);
                return;
            }

            int fontCount = 0;
            try (var files = Files.walk(fontDir)) {
                var fontFiles = files.filter(path -> {
                    String fileName = path.toString().toLowerCase();
                    return fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc");
                }).toList();

                for (Path fontFile : fontFiles) {
                    try {
                        String fontFamily = extractFontFamily(fontFile);
                        renderer.getFontResolver().addFont(fontFile.toString(), fontFamily, true);
                        fontCount++;
                        log.debug("加载自定义字体: {} -> {}", fontFamily, fontFile);
                    } catch (Exception e) {
                        log.debug("加载自定义字体失败: {}", fontFile, e);
                    }
                }
            }

            // 标记为已加载
            customFontDirCache.put(fontPath, true);
            log.info("自定义字体目录加载完成: {}, 共加载 {} 个字体", fontPath, fontCount);

        } catch (Exception e) {
            log.warn("加载自定义字体目录失败: {}", fontPath, e);
        }
    }

    /**
     * 加载系统字体（只加载一次）
     */
    private static void loadSystemFonts(ITextRenderer renderer) {
        if (systemFontsLoaded) {
            log.debug("系统字体已加载，跳过");
            return;
        }

        synchronized (HtmlToPdfGenerator.class) {
            if (systemFontsLoaded) {
                return;
            }

            try {
                log.debug("开始加载系统字体...");

                // 按优先级加载系统字体目录
                String[] systemFontPaths = {
                    // Linux
                    "/usr/share/fonts",
                    "/usr/local/share/fonts",
                    // Windows
                    "C:/Windows/Fonts",
                    // macOS
                    "/System/Library/Fonts",
                    "/Library/Fonts"
                };

                int totalFontCount = 0;
                for (String fontPath : systemFontPaths) {
                    totalFontCount += loadFontsFromDirectory(renderer, fontPath);
                }

                systemFontsLoaded = true;
                log.info("系统字体加载完成，共加载 {} 个字体", totalFontCount);

            } catch (Exception e) {
                log.warn("加载系统字体失败", e);
            }
        }
    }

    /**
     * 从指定目录加载字体
     */
    private static int loadFontsFromDirectory(ITextRenderer renderer, String dirPath) {
        try {
            Path fontDir = Paths.get(dirPath);
            if (!Files.exists(fontDir) || !Files.isDirectory(fontDir)) {
                return 0;
            }

            int fontCount = 0;
            try (var files = Files.walk(fontDir, 2)) {
                var fontFiles = files.filter(path -> {
                    String fileName = path.toString().toLowerCase();
                    // 优先加载中文字体
                    return (fileName.contains("sim") || fileName.contains("microsoft") ||
                           fileName.contains("noto") || fileName.contains("source") ||
                           fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc"));
                }).toList();

                for (Path fontFile : fontFiles) {
                    try {
                        String fontFamily = extractFontFamily(fontFile);
                        renderer.getFontResolver().addFont(fontFile.toString(), fontFamily, true);
                        fontCount++;
                        log.debug("加载系统字体: {} -> {}", fontFamily, fontFile);
                    } catch (Exception e) {
                        log.debug("加载系统字体失败: {}", fontFile, e);
                    }
                }
            }

            if (fontCount > 0) {
                log.debug("从目录 {} 加载了 {} 个字体", dirPath, fontCount);
            }
            return fontCount;

        } catch (Exception e) {
            log.debug("扫描字体目录失败: {}", dirPath, e);
            return 0;
        }
    }

    /**
     * 从字体文件路径提取字体族名称
     */
    private static String extractFontFamily(Path fontPath) {
        String fileName = fontPath.getFileName().toString();
        // 移除扩展名
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            fileName = fileName.substring(0, dotIndex);
        }
        return fileName;
    }

    /**
     * 确定字体路径
     */
    private static String determineFontPath(PdfRenderOptions options) {
        // 优先使用环境变量
        String envFontPath = System.getenv("FONT_PATH");
        if (!Str.isEmpty(envFontPath)) {
            return envFontPath;
        }

        // 使用配置的字体路径
        if (!Str.isEmpty(options.getFontPath())) {
            return options.getFontPath();
        }

        return null;
    }

    /**
     * 安全关闭资源的工具方法
     */
    private static void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                log.debug("关闭资源时出现异常", e);
            }
        }
    }

    /**
     * 安全关闭PdfStamper
     */
    private static void closeQuietly(PdfStamper stamper) {
        if (stamper != null) {
            try {
                stamper.close();
            } catch (Exception e) {
                log.debug("关闭PdfStamper时出现异常", e);
            }
        }
    }

    /**
     * 安全关闭PdfReader
     */
    private static void closeQuietly(PdfReader reader) {
        if (reader != null) {
            try {
                reader.close();
            } catch (Exception e) {
                log.debug("关闭PdfReader时出现异常", e);
            }
        }
    }
}


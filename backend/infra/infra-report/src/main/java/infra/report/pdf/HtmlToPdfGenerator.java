package infra.report.pdf;

import com.lowagie.text.FontFactory;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;
import org.openpdf.pdf.ITextRenderer;

import java.io.*;
import java.net.URL;

/**
 * HTML转PDF生成器
 * 使用ITextRenderer进行HTML到PDF的转换，支持现代CSS和外部资源
 */
@Slf4j
public class HtmlToPdfGenerator implements AutoCloseable {
    private final PdfRenderOptions options;
    private ITextRenderer renderer;

    /**
     * 创建HTML转PDF生成器
     *
     * @param options PDF渲染选项
     */
    public HtmlToPdfGenerator(PdfRenderOptions options) {
        this.options = options != null ? options : new PdfRenderOptions();
        initializeRenderer();
    }

    /**
     * 初始化渲染器和字体
     */
    private void initializeRenderer() {
        try {
            renderer = new ITextRenderer();
            initializeFonts();
            log.debug("PDF渲染器初始化成功");
        } catch (Exception e) {
            log.error("PDF渲染器初始化失败", e);
            throw new PdfException("PDF渲染器初始化失败", e);
        }
    }

    /**
     * 初始化字体支持
     */
    private void initializeFonts() {
        try {
            // 使用FontManager管理字体（单例，线程安全）
            FontManager fontManager = FontManager.getInstance();

            // 只有在未初始化或字体路径发生变化时才初始化
            fontManager.initialize(options.getExternalFontPath());

            // 注册自定义字体（如果配置了）
            if (!Str.isEmpty(options.getFontPath())) {
                registerCustomFont();
            }

            if (log.isDebugEnabled()) {
                log.debug("字体初始化完成，已注册字体数量: {}", fontManager.getRegisteredFonts().size());
            }
        } catch (Exception e) {
            log.warn("字体初始化失败", e);
        }
    }

    /**
     * 注册自定义字体
     */
    private void registerCustomFont() {
        if (Str.isEmpty(options.getFontPath())) {
            return;
        }

        try {
            // 尝试作为文件路径注册
            FontManager fontManager = FontManager.getInstance();
            if (fontManager.registerFontFile(options.getFontPath())) {
                log.info("自定义字体注册成功: {}", options.getFontPath());
                return;
            }

            // 尝试作为classpath资源注册
            URL fontUrl = getClass().getClassLoader().getResource(options.getFontPath());
            if (fontUrl != null) {
                String fontName = extractFontNameFromPath(options.getFontPath());
                FontFactory.register(fontUrl.toString(), fontName);
                log.info("自定义字体从classpath注册成功: {} -> {}", options.getFontPath(), fontName);
            } else {
                log.warn("自定义字体文件未找到: {}", options.getFontPath());
            }
        } catch (Exception e) {
            log.warn("自定义字体注册失败: {}", options.getFontPath(), e);
        }
    }

    /**
     * 从路径提取字体名称
     */
    private String extractFontNameFromPath(String fontPath) {
        String fileName = fontPath.substring(fontPath.lastIndexOf('/') + 1);
        int dotIndex = fileName.lastIndexOf('.');
        return dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName;
    }

    /**
     * 将HTML转换为PDF
     */
    public byte[] generatePdf(String html) throws Exception {
        return generatePdf(html, null);
    }

    /**
     * 将HTML转换为PDF
     */
    public byte[] generatePdf(String html, String cssContent) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generatePdf(html, cssContent, outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * 将HTML转换为PDF到输出流
     */
    public void generatePdf(String html, String cssContent, OutputStream outputStream) {
        validateInputs(html, outputStream);

        try {
            // 预处理HTML内容
            String processedHtml = preprocessHtml(html, cssContent);

            // 设置HTML文档
            if (!Str.isEmpty(options.getBaseUrl())) {
                renderer.setDocumentFromString(processedHtml, options.getBaseUrl());
            } else {
                renderer.setDocumentFromString(processedHtml);
            }

            // 布局文档
            renderer.layout();

            // 生成PDF
            renderer.createPDF(outputStream);

            log.debug("PDF生成成功");

        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw new PdfException("PDF生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证输入参数
     */
    private void validateInputs(String html, OutputStream outputStream) {
        if (Str.isEmpty(html)) {
            throw new IllegalArgumentException("html内容不能为空");
        }
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }
    }

    /**
     * 预处理HTML内容
     */
    private String preprocessHtml(String html, String cssContent) {
        // 如果HTML已经是完整文档，直接处理CSS注入
        String htmlTrim = html.trim().toLowerCase();
        if (htmlTrim.startsWith("<!doctype") || htmlTrim.startsWith("<html")) {
            return injectAdditionalCss(html, cssContent);
        }

        // 构建完整的HTML文档
        StringBuilder processedHtml = new StringBuilder();
        processedHtml.append("<!DOCTYPE html>\n");
        processedHtml.append("<html>\n<head>\n");
        processedHtml.append("<meta charset=\"UTF-8\">\n");

        // 添加页面配置（用于页眉页脚等）
        if (options.getHeaderFooterOptions() != null) {
            processedHtml.append(generatePageCss());
        }

        // 添加默认样式
        processedHtml.append("<style>\n");
        processedHtml.append(getDefaultCss());

        // 添加自定义CSS
        if (!Str.isEmpty(cssContent)) {
            processedHtml.append("\n/* Custom CSS */\n");
            processedHtml.append(cssContent);
        }

        // 添加配置中的自定义CSS属性
        if (!options.getCustomCssProperties().isEmpty()) {
            processedHtml.append("\n/* Configuration CSS */\n");
            processedHtml.append("body {\n");
            options.getCustomCssProperties().forEach((key, value) ->
                    processedHtml.append("  ").append(key).append(": ").append(value).append(";\n"));
            processedHtml.append("}\n");
        }

        // 添加水印CSS（如果需要）
        if (options.getWatermarkOptions() != null) {
            processedHtml.append(generateWatermarkCss());
        }

        processedHtml.append("</style>\n");
        processedHtml.append("</head>\n<body>\n");

        processedHtml.append(html);
        processedHtml.append("\n</body>\n</html>");

        return processedHtml.toString();
    }

    /**
     * 获取默认CSS样式
     */
    private String getDefaultCss() {
        return String.format("""
                        /* 基础样式重置 */
                        * {
                            box-sizing: border-box;
                        }
                        
                        /* 页面基础样式 */
                        @page {
                            size: %s;
                            margin: %.1fpt %.1fpt %.1fpt %.1fpt;
                        }
                        
                        /* 字体和排版 */
                        body {
                            font-family: '%s', 'SimHei', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
                            font-size: %.1fpt;
                            line-height: 1.6;
                            margin: 0;
                            padding: 20px;
                            color: #333;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                        }
                        
                        /* 标题样式 */
                        h1, h2, h3, h4, h5, h6 {
                            margin-top: 0;
                            margin-bottom: 0.5em;
                            font-weight: bold;
                            line-height: 1.2;
                        }
                        
                        /* 段落样式 */
                        p {
                            margin: 0 0 1em 0;
                        }
                        
                        /* 表格样式 */
                        table {
                            border-collapse: collapse;
                            width: 100%%;
                            margin: 1em 0;
                        }
                        
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px 12px;
                            text-align: left;
                            vertical-align: top;
                        }
                        
                        th {
                            background-color: #f8f9fa;
                            font-weight: bold;
                        }
                        
                        /* 图片样式 */
                        img {
                            max-width: 100%%;
                            height: auto;
                        }
                        """,
                getPageSizeString(),
                options.getMarginTop(),
                options.getMarginRight(),
                options.getMarginBottom(),
                options.getMarginLeft(),
                options.getDefaultFontFamily(),
                options.getDefaultFontSize());
    }

    /**
     * 获取页面尺寸字符串
     */
    private String getPageSizeString() {
        // 简化处理，默认返回A4
        return "A4";
    }

    /**
     * 注入额外的CSS到现有HTML文档
     */
    private String injectAdditionalCss(String html, String cssContent) {
        if (Str.isEmpty(cssContent) && options.getCustomCssProperties().isEmpty()) {
            return html;
        }

        StringBuilder additionalCss = new StringBuilder();

        if (!Str.isEmpty(cssContent)) {
            additionalCss.append(cssContent);
        }

        if (!options.getCustomCssProperties().isEmpty()) {
            additionalCss.append("\nbody {\n");
            options.getCustomCssProperties().forEach((key, value) ->
                    additionalCss.append("  ").append(key).append(": ").append(value).append(";\n"));
            additionalCss.append("}\n");
        }

        // 查找</head>标签并在其前面插入CSS
        String lowerHtml = html.toLowerCase();
        int headEndIndex = lowerHtml.indexOf("</head>");

        if (headEndIndex != -1) {
            return html.substring(0, headEndIndex) +
                    "<style>\n" + additionalCss + "\n</style>\n" +
                    html.substring(headEndIndex);
        }

        return html;
    }

    /**
     * 生成页面CSS（用于页眉页脚）
     */
    private String generatePageCss() {
        if (options.getHeaderFooterOptions() == null) {
            return "";
        }

        var headerFooter = options.getHeaderFooterOptions();
        StringBuilder css = new StringBuilder();

        css.append("@page {\n");

        // 页眉
        if (!Str.isEmpty(headerFooter.getHeaderCenter())) {
            css.append("  @top-center {\n");
            css.append("    content: \"").append(headerFooter.getHeaderCenter()).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        if (!Str.isEmpty(headerFooter.getHeaderLeft())) {
            css.append("  @top-left {\n");
            css.append("    content: \"").append(headerFooter.getHeaderLeft()).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        if (!Str.isEmpty(headerFooter.getHeaderRight())) {
            css.append("  @top-right {\n");
            css.append("    content: \"").append(headerFooter.getHeaderRight()).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        // 页脚
        if (!Str.isEmpty(headerFooter.getFooterCenter())) {
            css.append("  @bottom-center {\n");
            css.append("    content: \"").append(headerFooter.getFooterCenter()).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        if (!Str.isEmpty(headerFooter.getFooterLeft())) {
            css.append("  @bottom-left {\n");
            css.append("    content: \"").append(headerFooter.getFooterLeft()).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        if (!Str.isEmpty(headerFooter.getFooterRight())) {
            css.append("  @bottom-right {\n");
            css.append("    content: \"").append(headerFooter.getFooterRight()).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        // 页码（只有在没有设置其他页脚内容时才显示）
        if (headerFooter.isShowPageNumbers() &&
            Str.isEmpty(headerFooter.getFooterLeft()) &&
            Str.isEmpty(headerFooter.getFooterCenter()) &&
            Str.isEmpty(headerFooter.getFooterRight())) {
            css.append("  @bottom-center {\n");
            css.append("    content: \"").append(headerFooter.getPageNumberFormat()
                    .replace("{page}", "\" counter(page) \"")
                    .replace("{total}", "\" counter(pages) \"")).append("\";\n");
            css.append("    font-size: ").append(headerFooter.getFontSize()).append("pt;\n");
            css.append("  }\n");
        }

        css.append("}\n");

        return css.toString();
    }

    /**
     * 生成水印CSS
     */
    private String generateWatermarkCss() {
        if (options.getWatermarkOptions() == null) {
            return "";
        }

        var watermark = options.getWatermarkOptions();
        StringBuilder css = new StringBuilder();

        if (!Str.isEmpty(watermark.getText())) {
            String[] position = calculateWatermarkPosition(watermark.getPosition());
            css.append("body::before {\n");
            css.append("  content: \"").append(escapeForCss(watermark.getText())).append("\";\n");
            css.append("  position: fixed;\n");
            css.append("  top: ").append(position[1]).append(";\n");
            css.append("  left: ").append(position[0]).append(";\n");
            css.append("  transform: translate(-50%, -50%) rotate(").append(watermark.getRotation()).append("deg);\n");
            css.append("  font-size: ").append(watermark.getFontSize()).append("pt;\n");
            css.append("  color: ").append(watermark.getColor()).append(";\n");
            css.append("  opacity: ").append(watermark.getOpacity()).append(";\n");
            css.append("  z-index: -1;\n");
            css.append("  pointer-events: none;\n");
            css.append("  white-space: nowrap;\n");
            css.append("}\n");
        }

        if (!Str.isEmpty(watermark.getImagePath())) {
            String imageUrl = resolveImageUrl(watermark.getImagePath());
            String[] position = calculateWatermarkPosition(watermark.getPosition());
            css.append("body::after {\n");
            css.append("  content: \"\";\n");
            css.append("  position: fixed;\n");
            css.append("  top: ").append(position[1]).append(";\n");
            css.append("  left: ").append(position[0]).append(";\n");
            css.append("  transform: translate(-50%, -50%) rotate(").append(watermark.getRotation()).append("deg);\n");
            css.append("  background-image: url('").append(imageUrl).append("');\n");
            css.append("  background-repeat: no-repeat;\n");
            css.append("  background-size: contain;\n");
            css.append("  background-position: center;\n");
            css.append("  opacity: ").append(watermark.getOpacity()).append(";\n");
            css.append("  z-index: -1;\n");
            css.append("  pointer-events: none;\n");
            css.append("  width: 200px;\n");
            css.append("  height: 200px;\n");
            css.append("}\n");
        }

        return css.toString();
    }

    /**
     * 计算水印位置
     */
    private String[] calculateWatermarkPosition(String position) {
        return switch (position.toLowerCase()) {
            case "top-left" -> new String[]{"10%", "10%"};
            case "top-right" -> new String[]{"90%", "10%"};
            case "bottom-left" -> new String[]{"10%", "90%"};
            case "bottom-right" -> new String[]{"90%", "90%"};
            case "top-center" -> new String[]{"50%", "10%"};
            case "bottom-center" -> new String[]{"50%", "90%"};
            case "center-left" -> new String[]{"10%", "50%"};
            case "center-right" -> new String[]{"90%", "50%"};
            default -> new String[]{"50%", "50%"}; // center
        };
    }

    /**
     * 转义CSS字符串
     */
    private String escapeForCss(String text) {
        if (Str.isEmpty(text)) {
            return "";
        }
        return text.replace("\"", "\\\"")
                  .replace("\\", "\\\\")
                  .replace("\n", "\\A")
                  .replace("\r", "");
    }

    /**
     * 解析图片URL，支持多种来源
     */
    private String resolveImageUrl(String imagePath) {
        if (Str.isEmpty(imagePath)) {
            return "";
        }

        // 如果是HTTP/HTTPS URL，直接返回
        if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
            return imagePath;
        }

        // 如果是data URL，直接返回
        if (imagePath.startsWith("data:")) {
            return imagePath;
        }

        // 尝试从classpath加载
        try {
            URL resourceUrl = getClass().getClassLoader().getResource(imagePath);
            if (resourceUrl != null) {
                return resourceUrl.toString();
            }
        } catch (Exception e) {
            log.debug("从classpath加载图片失败: {}", imagePath);
        }

        // 如果以/static/开头，转换为classpath路径
        if (imagePath.startsWith("/static/")) {
            String resourcePath = "static" + imagePath.substring(7);
            try {
                URL resourceUrl = getClass().getClassLoader().getResource(resourcePath);
                if (resourceUrl != null) {
                    return resourceUrl.toString();
                }
            } catch (Exception e) {
                log.debug("从static目录加载图片失败: {}", resourcePath);
            }
        }

        // 如果baseUrl存在，尝试相对路径解析
        if (!Str.isEmpty(options.getBaseUrl())) {
            try {
                if (!options.getBaseUrl().endsWith("/") && !imagePath.startsWith("/")) {
                    return options.getBaseUrl() + "/" + imagePath;
                } else {
                    return options.getBaseUrl() + imagePath;
                }
            } catch (Exception e) {
                log.debug("使用baseUrl解析图片路径失败: {}", imagePath);
            }
        }

        // 最后直接返回原路径
        log.warn("无法解析图片路径: {}", imagePath);
        return imagePath;
    }

    /**
     * 从HTML生成PDF
     */
    public static byte[] generatePdfFromHtml(String html) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(new PdfRenderOptions())) {
            return generator.generatePdf(html);
        } catch (Exception e) {
            throw new PdfException("生成PDF失败", e);
        }
    }

    /**
     * 从HTML生成PDF（自定义选项）
     */
    public static byte[] generatePdfFromHtml(String html, PdfRenderOptions options) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
            return generator.generatePdf(html);
        } catch (Exception e) {
            throw new PdfException("生成PDF失败", e);
        }
    }

    /**
     * 从HTML生成PDF（自定义CSS）
     */
    public static byte[] generatePdfFromHtml(String html, String css) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(new PdfRenderOptions())) {
            return generator.generatePdf(html, css);
        } catch (Exception e) {
            throw new PdfException("生成PDF失败", e);
        }
    }

    /**
     * 从HTML生成PDF（自定义选项和CSS）
     */
    public static byte[] generatePdfFromHtml(String html, String css, PdfRenderOptions options) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
            return generator.generatePdf(html, css);
        } catch (Exception e) {
            throw new PdfException("生成PDF失败", e);
        }
    }

    @Override
    public void close() {
        // 清理渲染器资源
        if (renderer != null) {
            try {
                renderer.finishPDF();
                log.debug("PDF渲染器资源已清理");
            } catch (Exception e) {
                log.debug("清理PDF渲染器时出现异常（可忽略）", e);
            } finally {
                renderer = null;
            }
        }
        log.debug("HtmlToPdfGenerator已关闭");
    }
}

package infra.report.pdf;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.slf4j.Slf4jLogger;
import com.openhtmltopdf.util.XRLog;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.imageio.ImageIO;

/**
 * 基于OpenHtmlToPdf 1.1.9的HTML转PDF生成器
 * 优化版本：简化代码，提升性能，减少内存占用
 */
@Slf4j
public class HtmlToPdfGenerator {

    // 字体缓存
    private static final Map<String, String> fontCache = new ConcurrentHashMap<>();

    /**
     * 生成PDF - 主入口方法
     */
    public static byte[] generatePdf(String htmlContent, PdfRenderOptions options) {
        try {
            // 配置日志
            setupLogging();

            // 预处理HTML
            String processedHtml = preprocessHtml(htmlContent, options);

            // 初始化字体
            initializeFonts(options);

            // 生成基础PDF
            byte[] basePdf = generateBasePdf(processedHtml, options);

            // 添加页眉页脚和水印
            return addHeaderFooterAndWatermark(basePdf, options);

        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw new RuntimeException("PDF生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 配置日志
     */
    private static void setupLogging() {
        XRLog.setLoggerImpl(new Slf4jLogger());
        XRLog.setLoggingEnabled(false); // 关闭详细日志以提升性能
    }

    /**
     * 预处理HTML
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        if (Str.isEmpty(htmlContent)) {
            return "<!DOCTYPE html><html><head><meta charset=\"UTF-8\" /></head><body></body></html>";
        }

        // 修复HTML使其符合XHTML标准
        htmlContent = fixHtmlForXhtml(htmlContent);

        // 注入CSS
        String css = buildCss(options);

        // 确保HTML结构完整
        if (!htmlContent.toLowerCase().contains("<head>")) {
            if (htmlContent.toLowerCase().contains("<html>")) {
                htmlContent = htmlContent.replaceFirst("(?i)<html[^>]*>",
                    "$0<head><meta charset=\"UTF-8\" /><style>" + css + "</style></head>");
            } else {
                htmlContent = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\" /><style>" + css + "</style></head><body>"
                    + htmlContent + "</body></html>";
            }
        } else {
            if (!htmlContent.toLowerCase().contains("<meta")) {
                htmlContent = htmlContent.replaceFirst("(?i)<head>", "$0<meta charset=\"UTF-8\" />");
            }
            htmlContent = htmlContent.replaceFirst("(?i)</head>", "<style>" + css + "</style>$0");
        }

        return htmlContent;
    }

    /**
     * 修复HTML使其符合XHTML标准
     */
    private static String fixHtmlForXhtml(String html) {
        if (html == null) return "";

        // 修复自闭合标签
        html = html.replaceAll("(?i)<meta([^>]*?)(?<!/)>", "<meta$1 />");
        html = html.replaceAll("(?i)<br(?:\\s[^>]*?)?>", "<br />");
        html = html.replaceAll("(?i)<hr(?:\\s[^>]*?)?>", "<hr />");
        html = html.replaceAll("(?i)<img([^>]*?)(?<!/)>", "<img$1 />");
        html = html.replaceAll("(?i)<input([^>]*?)(?<!/)>", "<input$1 />");
        html = html.replaceAll("(?i)<link([^>]*?)(?<!/)>", "<link$1 />");

        // 确保有DOCTYPE声明
        if (!html.toLowerCase().contains("<!doctype")) {
            html = "<!DOCTYPE html>\n" + html;
        }

        return html;
    }

    /**
     * 构建CSS
     */
    private static String buildCss(PdfRenderOptions options) {
        StringBuilder css = new StringBuilder();

        // 页面设置
        css.append("@page { ");
        css.append("margin-top: ").append(options.getMarginTop()).append("pt; ");
        css.append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ");
        css.append("margin-left: ").append(options.getMarginLeft()).append("pt; ");
        css.append("margin-right: ").append(options.getMarginRight()).append("pt; ");

        // 页面大小
        if (options.getPageSize() != null) {
            css.append("size: ").append(getPageSizeCss(options.getPageSize())).append("; ");
        }
        css.append("} ");

        // 字体设置
        css.append("body { ");
        css.append("font-family: '").append(options.getDefaultFontFamily()).append("', ");
        css.append("'SimHei', 'Microsoft YaHei', sans-serif; ");
        css.append("font-size: ").append(options.getDefaultFontSize()).append("pt; ");
        css.append("} ");

        // 自定义CSS
        if (options.getCustomCssProperties() != null) {
            options.getCustomCssProperties().forEach((selector, rules) ->
                css.append(selector).append(" { ").append(rules).append(" } "));
        }

        return css.toString();
    }

    /**
     * 获取页面大小CSS
     */
    private static String getPageSizeCss(PdfRenderOptions.PageSize pageSize) {
        return switch (pageSize) {
            case A4 -> "A4";
            case A3 -> "A3";
            case A5 -> "A5";
            case LETTER -> "letter";
            case LEGAL -> "legal";
        };
    }

    /**
     * 初始化字体
     */
    private static void initializeFonts(PdfRenderOptions options) {
        if (fontCache.isEmpty()) {
            synchronized (fontCache) {
                if (fontCache.isEmpty()) {
                    try {
                        // 扫描资源字体
                        scanResourceFonts();

                        // 扫描外部字体
                        String externalFontPath = determineFontPath(options);
                        if (!Str.isEmpty(externalFontPath)) {
                            scanExternalFonts(externalFontPath);
                        }

                        log.info("字体初始化完成，发现 {} 个字体", fontCache.size());
                    } catch (Exception e) {
                        log.warn("字体初始化失败", e);
                    }
                }
            }
        }
    }

    /**
     * 扫描资源字体
     */
    private static void scanResourceFonts() {
        try {
            String[] fontPaths = {"fonts/simhei.ttf", "fonts/simsun.ttf", "fonts/NotoSansCJK-Regular.ttc"};
            for (String fontPath : fontPaths) {
                try (InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader().getResourceAsStream(fontPath)) {
                    if (fontStream != null) {
                        String fontFamily = extractFontFamily(fontPath);
                        fontCache.put(fontFamily, "classpath:" + fontPath);
                        log.debug("发现资源字体: {}", fontFamily);
                    }
                } catch (Exception e) {
                    log.debug("加载资源字体失败: {}", fontPath);
                }
            }
        } catch (Exception e) {
            log.debug("扫描资源字体失败", e);
        }
    }

    /**
     * 扫描外部字体
     */
    private static void scanExternalFonts(String fontDir) {
        try {
            Path fontPath = Paths.get(fontDir);
            if (Files.exists(fontPath) && Files.isDirectory(fontPath)) {
                Files.walk(fontPath)
                    .filter(path -> path.toString().toLowerCase().matches(".*\\.(ttf|otf|ttc)$"))
                    .forEach(path -> {
                        try {
                            String fontFamily = extractFontFamily(path.getFileName().toString());
                            fontCache.put(fontFamily, path.toString());
                            log.debug("发现外部字体: {} -> {}", fontFamily, path);
                        } catch (Exception e) {
                            log.debug("处理外部字体失败: {}", path);
                        }
                    });
            }
        } catch (Exception e) {
            log.debug("扫描外部字体失败: {}", fontDir);
        }
    }

    /**
     * 提取字体族名称
     */
    private static String extractFontFamily(String fileName) {
        String name = fileName.toLowerCase();
        if (name.contains("simhei")) return "simhei";
        if (name.contains("simsun")) return "simsun";
        if (name.contains("notosans")) return "notosans";
        if (name.contains("sourcehansans")) return "sourcehansans";

        // 默认使用文件名（去掉扩展名）
        int dotIndex = fileName.lastIndexOf('.');
        return dotIndex > 0 ? fileName.substring(0, dotIndex).toLowerCase() : fileName.toLowerCase();
    }

    /**
     * 确定字体路径
     */
    private static String determineFontPath(PdfRenderOptions options) {
        if (options != null && !Str.isEmpty(options.getFontPath())) {
            return options.getFontPath();
        }

        // 尝试常见的字体目录
        String[] commonPaths = {"/usr/share/fonts", "/System/Library/Fonts", "C:\\Windows\\Fonts"};
        for (String path : commonPaths) {
            if (Files.exists(Paths.get(path))) {
                return path;
            }
        }

        return null;
    }

    /**
     * 生成基础PDF
     */
    private static byte[] generateBasePdf(String htmlContent, PdfRenderOptions options) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            PdfRendererBuilder builder = new PdfRendererBuilder();

            // 设置HTML内容
            builder.withHtmlContent(htmlContent, getBaseUri());

            // 配置输出流
            builder.toStream(outputStream);

            // 设置字体
            setupFonts(builder);

            // 设置页面配置
            setupPageConfiguration(builder, options);

            // 运行渲染
            builder.run();

            return outputStream.toByteArray();
        }
    }

    /**
     * 获取基础URI
     */
    private static String getBaseUri() {
        try {
            return ServletUtil.getRequest().getRequestURL().toString();
        } catch (Exception e) {
            return "http://localhost:8080/";
        }
    }

    /**
     * 设置字体
     */
    private static void setupFonts(PdfRendererBuilder builder) {
        try {
            for (Map.Entry<String, String> entry : fontCache.entrySet()) {
                String fontFamily = entry.getKey();
                String fontPath = entry.getValue();

                try {
                    if (fontPath.startsWith("classpath:")) {
                        String resourcePath = fontPath.substring("classpath:".length());
                        InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                                .getResourceAsStream(resourcePath);
                        if (fontStream != null) {
                            builder.useFont(() -> fontStream, fontFamily);
                        }
                    } else {
                        builder.useFont(new File(fontPath), fontFamily);
                    }
                } catch (Exception e) {
                    log.debug("字体注册失败: {} -> {}", fontFamily, fontPath);
                }
            }
        } catch (Exception e) {
            log.warn("设置字体失败", e);
        }
    }

    /**
     * 设置页面配置
     */
    private static void setupPageConfiguration(PdfRendererBuilder builder, PdfRenderOptions options) {
        try {
            // 设置页面大小
            if (options.getPageSize() != null) {
                switch (options.getPageSize()) {
                    case A4 -> builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM);
                    case A3 -> builder.useDefaultPageSize(297, 420, PdfRendererBuilder.PageSizeUnits.MM);
                    case A5 -> builder.useDefaultPageSize(148, 210, PdfRendererBuilder.PageSizeUnits.MM);
                    case LETTER -> builder.useDefaultPageSize(8.5f, 11f, PdfRendererBuilder.PageSizeUnits.INCHES);
                    case LEGAL -> builder.useDefaultPageSize(8.5f, 14f, PdfRendererBuilder.PageSizeUnits.INCHES);
                }
            } else {
                builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM);
            }

            // 启用快速模式
            builder.useFastMode();

            // 设置PDF版本
            builder.usePdfVersion(1.7f);

        } catch (Exception e) {
            log.warn("设置页面配置失败", e);
        }
    }

    /**
     * 添加页眉页脚和水印
     */
    private static byte[] addHeaderFooterAndWatermark(byte[] pdfBytes, PdfRenderOptions options) {
        if (options.getHeaderFooterOptions() == null && options.getWatermarkOptions() == null) {
            return pdfBytes; // 无需处理
        }

        try (PDDocument document = Loader.loadPDF(pdfBytes);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            PDFont font = loadDefaultFont(document, options);
            int totalPages = document.getNumberOfPages();

            for (int i = 0; i < totalPages; i++) {
                PDPage page = document.getPage(i);

                try (PDPageContentStream contentStream = new PDPageContentStream(document, page,
                        PDPageContentStream.AppendMode.APPEND, true, true)) {

                    // 添加页眉页脚
                    if (options.getHeaderFooterOptions() != null) {
                        addHeaderFooter(contentStream, page, font, options.getHeaderFooterOptions(), i + 1, totalPages);
                    }

                    // 添加水印
                    if (options.getWatermarkOptions() != null) {
                        addWatermark(contentStream, page, font, options.getWatermarkOptions(), document);
                    }
                }
            }

            document.save(outputStream);
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.warn("添加页眉页脚和水印失败，返回原始PDF", e);
            return pdfBytes;
        }
    }

    /**
     * 加载默认字体
     */
    private static PDFont loadDefaultFont(PDDocument document, PdfRenderOptions options) {
        try {
            String fontFamily = options.getDefaultFontFamily();
            String fontPath = fontCache.get(fontFamily);

            if (fontPath != null) {
                if (fontPath.startsWith("classpath:")) {
                    String resourcePath = fontPath.substring("classpath:".length());
                    InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                            .getResourceAsStream(resourcePath);
                    if (fontStream != null) {
                        return PDType0Font.load(document, fontStream);
                    }
                } else {
                    return PDType0Font.load(document, new File(fontPath));
                }
            }

            // 回退到默认字体
            return new PDType1Font(Standard14Fonts.FontName.HELVETICA);

        } catch (Exception e) {
            log.debug("加载字体失败，使用默认字体", e);
            try {
                return new PDType1Font(Standard14Fonts.FontName.HELVETICA);
            } catch (Exception e2) {
                throw new RuntimeException("无法加载字体", e2);
            }
        }
    }

    /**
     * 添加页眉页脚
     */
    private static void addHeaderFooter(PDPageContentStream contentStream, PDPage page, PDFont font,
                                      PdfRenderOptions.HeaderFooterOptions options, int pageNumber, int totalPages) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();
            float fontSize = options.getFontSize();

            contentStream.setFont(font, fontSize);

            // 页眉
            float headerY = pageHeight - 30;
            addHeaderFooterText(contentStream, options.getHeaderLeft(), options.getHeaderLeftColor(), 50, headerY);
            addHeaderFooterText(contentStream, options.getHeaderCenter(), options.getHeaderCenterColor(), pageWidth / 2, headerY);
            addHeaderFooterText(contentStream, options.getHeaderRight(), options.getHeaderRightColor(), pageWidth - 50, headerY);

            // 页脚
            float footerY = 30;
            addHeaderFooterText(contentStream, options.getFooterLeft(), options.getFooterLeftColor(), 50, footerY);
            addHeaderFooterText(contentStream, options.getFooterCenter(), options.getFooterCenterColor(), pageWidth / 2, footerY);

            String footerRight = replacePlaceholders(options.getFooterRight(), pageNumber, totalPages);
            addHeaderFooterText(contentStream, footerRight, options.getFooterRightColor(), pageWidth - 50, footerY);

        } catch (Exception e) {
            log.warn("添加页眉页脚失败", e);
        }
    }

    /**
     * 添加页眉页脚文本
     */
    private static void addHeaderFooterText(PDPageContentStream contentStream, String text, String color, float x, float y) {
        if (Str.isEmpty(text)) return;

        try {
            contentStream.setNonStrokingColor(parseColor(color));
            contentStream.beginText();
            contentStream.newLineAtOffset(x, y);
            contentStream.showText(text);
            contentStream.endText();
        } catch (Exception e) {
            log.debug("添加页眉页脚文本失败: {}", text);
        }
    }

    /**
     * 添加水印
     */
    private static void addWatermark(PDPageContentStream contentStream, PDPage page, PDFont font,
                                   PdfRenderOptions.WatermarkOptions options, PDDocument document) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(options.getOpacity());
            contentStream.setGraphicsStateParameters(graphicsState);

            // 优先尝试图片水印
            boolean imageWatermarkAdded = false;
            if (!Str.isEmpty(options.getImagePath())) {
                imageWatermarkAdded = addImageWatermark(contentStream, page, options, pageWidth, pageHeight, document);
            }

            // 如果图片水印失败，使用文本水印
            if (!imageWatermarkAdded && !Str.isEmpty(options.getText())) {
                addTextWatermark(contentStream, page, font, options, pageWidth, pageHeight);
            }

        } catch (Exception e) {
            log.warn("添加水印失败", e);
        }
    }

    /**
     * 添加文本水印
     */
    private static void addTextWatermark(PDPageContentStream contentStream, PDPage page, PDFont font,
                                       PdfRenderOptions.WatermarkOptions options, float pageWidth, float pageHeight) {
        try {
            contentStream.setFont(font, options.getFontSize());
            contentStream.setNonStrokingColor(parseColor(options.getColor()));

            String text = options.getText();
            float textWidth = font.getStringWidth(text) / 1000 * options.getFontSize();

            if (options.isRepeatWatermark()) {
                // 循环水印
                float horizontalSpacing = options.getHorizontalSpacing();
                float verticalSpacing = options.getVerticalSpacing();
                float density = options.getDensity();

                horizontalSpacing = horizontalSpacing / density;
                verticalSpacing = verticalSpacing / density;

                for (float y = 0; y < pageHeight; y += verticalSpacing) {
                    for (float x = 0; x < pageWidth; x += horizontalSpacing) {
                        addSingleTextWatermark(contentStream, text, x, y, options.getRotation());
                    }
                }
            } else {
                // 单个水印
                float x = (pageWidth - textWidth) / 2;
                float y = pageHeight / 2;
                addSingleTextWatermark(contentStream, text, x, y, options.getRotation());
            }

        } catch (Exception e) {
            log.warn("添加文本水印失败", e);
        }
    }

    /**
     * 添加单个文本水印
     */
    private static void addSingleTextWatermark(PDPageContentStream contentStream, String text,
                                             float x, float y, float rotation) {
        try {
            contentStream.saveGraphicsState();
            contentStream.transform(Matrix.getRotateInstance(Math.toRadians(rotation), x, y));
            contentStream.beginText();
            contentStream.newLineAtOffset(x, y);
            contentStream.showText(text);
            contentStream.endText();
            contentStream.restoreGraphicsState();
        } catch (Exception e) {
            log.debug("添加单个文本水印失败", e);
        }
    }

    /**
     * 添加图片水印
     */
    private static boolean addImageWatermark(PDPageContentStream contentStream, PDPage page,
                                           PdfRenderOptions.WatermarkOptions options, float pageWidth, float pageHeight, PDDocument document) {
        try {
            BufferedImage image = loadWatermarkImage(options.getImagePath());
            if (image == null) {
                return false;
            }

            // 创建PDImageXObject
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", baos);

            org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject pdImage =
                org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject.createFromByteArray(
                    document, baos.toByteArray(), "watermark");

            float imageWidth = image.getWidth() * 0.3f; // 缩放到30%
            float imageHeight = image.getHeight() * 0.3f;

            if (options.isRepeatWatermark()) {
                // 循环图片水印
                float horizontalSpacing = options.getHorizontalSpacing() / options.getDensity();
                float verticalSpacing = options.getVerticalSpacing() / options.getDensity();

                for (float y = 0; y < pageHeight; y += verticalSpacing) {
                    for (float x = 0; x < pageWidth; x += horizontalSpacing) {
                        contentStream.drawImage(pdImage, x, y, imageWidth, imageHeight);
                    }
                }
            } else {
                // 单个图片水印
                float x = (pageWidth - imageWidth) / 2;
                float y = (pageHeight - imageHeight) / 2;
                contentStream.drawImage(pdImage, x, y, imageWidth, imageHeight);
            }

            return true;

        } catch (Exception e) {
            log.warn("添加图片水印失败", e);
            return false;
        }
    }

    /**
     * 加载水印图片
     */
    private static BufferedImage loadWatermarkImage(String imagePath) {
        try {
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return ImageIO.read(URI.create(imagePath).toURL());
            } else if (imagePath.startsWith("classpath:")) {
                String resourcePath = imagePath.substring("classpath:".length());
                InputStream imageStream = HtmlToPdfGeneratorNew.class.getClassLoader()
                        .getResourceAsStream(resourcePath);
                return imageStream != null ? ImageIO.read(imageStream) : null;
            } else {
                File imageFile = new File(imagePath);
                return imageFile.exists() ? ImageIO.read(imageFile) : null;
            }
        } catch (Exception e) {
            log.warn("加载水印图片失败: {}", imagePath);
            return null;
        }
    }

    /**
     * 替换占位符
     */
    private static String replacePlaceholders(String text, int pageNumber, int totalPages) {
        if (text == null) return "";
        return text.replace("{page}", String.valueOf(pageNumber))
                  .replace("{total}", String.valueOf(totalPages));
    }

    /**
     * 解析颜色
     */
    private static Color parseColor(String colorStr) {
        if (Str.isEmpty(colorStr)) return Color.BLACK;

        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                return switch (colorStr.toLowerCase()) {
                    case "black" -> Color.BLACK;
                    case "white" -> Color.WHITE;
                    case "red" -> Color.RED;
                    case "green" -> Color.GREEN;
                    case "blue" -> Color.BLUE;
                    case "gray", "grey" -> Color.GRAY;
                    default -> Color.BLACK;
                };
            }
        } catch (Exception e) {
            return Color.BLACK;
        }
    }
}

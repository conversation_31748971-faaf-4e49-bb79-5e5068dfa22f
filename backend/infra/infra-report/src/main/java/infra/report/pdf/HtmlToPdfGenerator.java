package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.openpdf.pdf.ITextRenderer;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * HTML转PDF生成器 - 基于OpenPDF 2.2.4
 * 支持水印、页眉页脚、外部资源
 */
@Slf4j
public class HtmlToPdfGenerator {

    /**
     * 将HTML转换为PDF
     */
    public static void generatePdf(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        if (Str.isEmpty(htmlContent)) {
            throw new PdfException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new PdfException("输出流不能为空");
        }

        if (options == null) {
            options = new PdfRenderOptions();
        }

        // 自动设置baseUrl
        options = ensureBaseUrl(options);

        try {
            // 如果需要水印或页眉页脚，使用传统PDF方式
            if (hasWatermarkOrHeaderFooter(options)) {
                generatePdfWithEvents(htmlContent, outputStream, options);
            } else {
                // 纯HTML转PDF，使用HTML渲染器
                generatePdfWithRenderer(htmlContent, outputStream, options);
            }
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new PdfException("生成PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 自动设置baseUrl
     */
    private static PdfRenderOptions ensureBaseUrl(PdfRenderOptions options) {
        if (options == null || !Str.isEmpty(options.getBaseUrl())) {
            return options;
        }

        try {
            var request = ServletUtil.getRequest();
            if (request != null) {
                String baseUrl = request.getScheme() + "://" +
                               request.getServerName() + ":" +
                               request.getServerPort() +
                               request.getContextPath();

                // 创建新的options对象
                return PdfRenderOptions.builder()
                        .marginLeft(options.getMarginLeft())
                        .marginRight(options.getMarginRight())
                        .marginTop(options.getMarginTop())
                        .marginBottom(options.getMarginBottom())
                        .defaultFontFamily(options.getDefaultFontFamily())
                        .defaultFontSize(options.getDefaultFontSize())
                        .fontPath(options.getFontPath())
                        .watermarkOptions(options.getWatermarkOptions())
                        .headerFooterOptions(options.getHeaderFooterOptions())
                        .customCssProperties(options.getCustomCssProperties())
                        .baseUrl(baseUrl)
                        .build();
            }
        } catch (Exception e) {
            log.debug("无法获取当前请求，跳过自动设置baseUrl", e);
        }

        return options;
    }

    /**
     * 检查是否需要水印或页眉页脚
     */
    private static boolean hasWatermarkOrHeaderFooter(PdfRenderOptions options) {
        return (options.getWatermarkOptions() != null &&
                (!Str.isEmpty(options.getWatermarkOptions().getText()) ||
                        !Str.isEmpty(options.getWatermarkOptions().getImagePath()))) ||
                (options.getHeaderFooterOptions() != null);
    }

    /**
     * 使用事件处理器生成PDF（支持水印和页眉页脚）
     */
    private static void generatePdfWithEvents(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        try {
            // 先生成基础HTML到PDF
            ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
            generatePdfWithRenderer(htmlContent, tempStream, options);

            // 读取生成的PDF并添加水印和页眉页脚
            byte[] pdfBytes = tempStream.toByteArray();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);

            PdfReader reader = new PdfReader(inputStream);
            PdfStamper stamper = new PdfStamper(reader, outputStream);

            // 添加水印和页眉页脚
            addWatermarkAndHeaderFooter(stamper, options);

            stamper.close();
            reader.close();

        } catch (Exception e) {
            throw new PdfException("生成带事件的PDF失败", e);
        }
    }

    /**
     * 使用HTML渲染器生成PDF（纯HTML转换）
     */
    private static void generatePdfWithRenderer(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        try {
            ITextRenderer renderer = new ITextRenderer();

            // 设置渲染选项
            renderer.getSharedContext().setPrint(true);
            renderer.getSharedContext().setInteractive(false);

            // 设置字体
            setupFonts(renderer, options);

            // 处理HTML内容
            String processedHtml = preprocessHtml(htmlContent, options);

            // 设置文档
            if (!Str.isEmpty(options.getBaseUrl())) {
                renderer.setDocumentFromString(processedHtml, options.getBaseUrl());
            } else {
                renderer.setDocumentFromString(processedHtml);
            }

            renderer.layout();
            renderer.createPDF(outputStream);

        } catch (Exception e) {
            throw new PdfException("HTML渲染失败", e);
        }
    }

    /**
     * 添加水印和页眉页脚到PDF
     */
    private static void addWatermarkAndHeaderFooter(PdfStamper stamper, PdfRenderOptions options) {
        try {
            int totalPages = stamper.getReader().getNumberOfPages();

            for (int i = 1; i <= totalPages; i++) {
                PdfContentByte canvas = stamper.getOverContent(i);
                Rectangle pageSize = stamper.getReader().getPageSize(i);

                // 添加水印
                if (options.getWatermarkOptions() != null) {
                    WatermarkProcessor.addWatermark(canvas, pageSize, options.getWatermarkOptions());
                }

                // 添加页眉页脚
                if (options.getHeaderFooterOptions() != null) {
                    addHeaderFooter(canvas, pageSize, options.getHeaderFooterOptions(), i, totalPages);
                }
            }
        } catch (Exception e) {
            log.error("添加水印和页眉页脚失败", e);
        }
    }

    /**
     * 添加页眉页脚
     */
    private static void addHeaderFooter(PdfContentByte canvas, Rectangle pageSize,
                                       PdfRenderOptions.HeaderFooterOptions options,
                                       int currentPage, int totalPages) {
        try {
            // 创建默认字体
            BaseFont baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            Font font = new Font(baseFont, options.getFontSize());

            float headerY = pageSize.getTop() - 20;
            float footerY = pageSize.getBottom() + 20;

            // 页眉
            if (!Str.isEmpty(options.getHeaderLeft())) {
                addText(canvas, options.getHeaderLeft(), 50, headerY, Element.ALIGN_LEFT, font);
            }
            if (!Str.isEmpty(options.getHeaderCenter())) {
                addText(canvas, options.getHeaderCenter(), pageSize.getWidth() / 2, headerY, Element.ALIGN_CENTER, font);
            }
            if (!Str.isEmpty(options.getHeaderRight())) {
                addText(canvas, options.getHeaderRight(), pageSize.getWidth() - 50, headerY, Element.ALIGN_RIGHT, font);
            }

            // 页脚
            if (!Str.isEmpty(options.getFooterLeft())) {
                String text = processPageNumberTemplate(options.getFooterLeft(), currentPage, totalPages);
                addText(canvas, text, 50, footerY, Element.ALIGN_LEFT, font);
            }
            if (!Str.isEmpty(options.getFooterCenter())) {
                String text = processPageNumberTemplate(options.getFooterCenter(), currentPage, totalPages);
                addText(canvas, text, pageSize.getWidth() / 2, footerY, Element.ALIGN_CENTER, font);
            }
            if (!Str.isEmpty(options.getFooterRight())) {
                String text = processPageNumberTemplate(options.getFooterRight(), currentPage, totalPages);
                addText(canvas, text, pageSize.getWidth() - 50, footerY, Element.ALIGN_RIGHT, font);
            }

            // 默认页码
            if (options.isShowPageNumbers() &&
                Str.isEmpty(options.getFooterLeft()) &&
                Str.isEmpty(options.getFooterCenter()) &&
                Str.isEmpty(options.getFooterRight())) {
                String pageText = processPageNumberTemplate(options.getPageNumberFormat(), currentPage, totalPages);
                addText(canvas, pageText, pageSize.getWidth() / 2, footerY, Element.ALIGN_CENTER, font);
            }

        } catch (Exception e) {
            log.error("添加页眉页脚失败", e);
        }
    }

    /**
     * 添加文本到PDF
     */
    private static void addText(PdfContentByte canvas, String text, float x, float y, int alignment, Font font) {
        try {
            Phrase phrase = new Phrase(text, font);
            ColumnText.showTextAligned(canvas, alignment, phrase, x, y, 0);
        } catch (Exception e) {
            log.error("添加文本失败", e);
        }
    }

    /**
     * 处理页码模板
     */
    private static String processPageNumberTemplate(String template, int currentPage, int totalPages) {
        if (Str.isEmpty(template)) {
            return "";
        }
        return template.replace("{page}", String.valueOf(currentPage))
                      .replace("{total}", String.valueOf(totalPages));
    }

    /**
     * 预处理HTML，注入页边距和自定义CSS
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        StringBuilder cssBuilder = new StringBuilder();

        // 添加页边距CSS
        cssBuilder.append("@page { ")
                .append("margin-top: ").append(options.getMarginTop()).append("pt; ")
                .append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ")
                .append("margin-left: ").append(options.getMarginLeft()).append("pt; ")
                .append("margin-right: ").append(options.getMarginRight()).append("pt; ")
                .append("} ");

        // 添加默认字体CSS
        cssBuilder.append("body { ")
                .append("font-family: '").append(options.getDefaultFontFamily()).append("', sans-serif; ")
                .append("font-size: ").append(options.getDefaultFontSize()).append("pt; ")
                .append("} ");

        // 添加自定义CSS属性
        if (options.getCustomCssProperties() != null) {
            options.getCustomCssProperties().forEach((selector, rules) ->
                    cssBuilder.append(selector).append(" { ").append(rules).append(" } "));
        }

        String cssContent = cssBuilder.toString();

        // 如果HTML中没有head标签，添加一个
        if (!htmlContent.toLowerCase().contains("<head>")) {
            if (htmlContent.toLowerCase().contains("<html>")) {
                htmlContent = htmlContent.replaceFirst("(?i)<html[^>]*>",
                        "$0<head><style>" + cssContent + "</style></head>");
            } else {
                htmlContent = "<html><head><style>" + cssContent + "</style></head><body>" +
                        htmlContent + "</body></html>";
            }
        } else {
            // 在head标签中添加CSS
            htmlContent = htmlContent.replaceFirst("(?i)</head>",
                    "<style>" + cssContent + "</style></head>");
        }

        return htmlContent;
    }





    /**
     * 设置字体
     */
    private static void setupFonts(ITextRenderer renderer, PdfRenderOptions options) {
        try {
            String fontPath = determineFontPath(options);

            // 添加中文字体支持
            if (!Str.isEmpty(fontPath)) {
                Path fontDir = Paths.get(fontPath);
                if (Files.exists(fontDir) && Files.isDirectory(fontDir)) {
                    // 扫描字体目录
                    Files.walk(fontDir)
                            .filter(path -> {
                                String fileName = path.toString().toLowerCase();
                                return fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc");
                            })
                            .forEach(path -> {
                                try {
                                    // OpenPDF 2.2.4 支持的字体添加方式
                                    String fontFamily = extractFontFamily(path);
                                    renderer.getFontResolver().addFont(path.toString(), fontFamily, true);
                                    log.debug("添加字体: {} -> {}", fontFamily, path);
                                } catch (Exception e) {
                                    log.warn("添加字体失败: {}", path, e);
                                }
                            });
                }
            }

            // 添加默认字体和内置字体
            addDefaultFonts(renderer);

        } catch (Exception e) {
            log.warn("设置字体失败", e);
            // 仅添加默认字体
            addDefaultFonts(renderer);
        }
    }

    /**
     * 从字体文件路径提取字体族名称
     */
    private static String extractFontFamily(Path fontPath) {
        String fileName = fontPath.getFileName().toString();
        // 移除扩展名
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            fileName = fileName.substring(0, dotIndex);
        }
        return fileName;
    }

    /**
     * 添加默认字体
     */
    private static void addDefaultFonts(ITextRenderer renderer) {
        try {
            // OpenPDF 2.2.4 内置字体支持
            String[] defaultFonts = {
                    "serif", "sans-serif", "monospace"
            };

            for (String fontFamily : defaultFonts) {
                try {
                    renderer.getFontResolver().addFont(fontFamily, true);
                    log.debug("添加内置字体: {}", fontFamily);
                } catch (Exception e) {
                    log.debug("无法添加内置字体: {}", fontFamily);
                }
            }

            // 尝试添加系统中文字体
            addSystemChineseFonts(renderer);

        } catch (Exception e) {
            log.warn("添加默认字体失败", e);
        }
    }

    /**
     * 添加系统中文字体
     */
    private static void addSystemChineseFonts(ITextRenderer renderer) {
        // Windows 字体路径
        addFontsFromDirectory(renderer, "C:/Windows/Fonts");
        // Linux 字体路径
        addFontsFromDirectory(renderer, "/usr/share/fonts");
        addFontsFromDirectory(renderer, "/usr/local/share/fonts");
        // macOS 字体路径
        addFontsFromDirectory(renderer, "/System/Library/Fonts");
        addFontsFromDirectory(renderer, "/Library/Fonts");
    }

    /**
     * 从指定目录添加字体
     */
    private static void addFontsFromDirectory(ITextRenderer renderer, String dirPath) {
        try {
            Path fontDir = Paths.get(dirPath);
            if (Files.exists(fontDir) && Files.isDirectory(fontDir)) {
                Files.walk(fontDir, 2) // 限制深度避免过深搜索
                        .filter(path -> {
                            String fileName = path.toString().toLowerCase();
                            return (fileName.contains("sim") || fileName.contains("microsoft") ||
                                    fileName.contains("noto") || fileName.contains("source")) &&
                                    (fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc"));
                        })
                        .limit(10) // 限制数量避免加载过多字体
                        .forEach(path -> {
                            try {
                                String fontFamily = extractFontFamily(path);
                                renderer.getFontResolver().addFont(path.toString(), fontFamily, true);
                                log.debug("添加系统字体: {} -> {}", fontFamily, path);
                            } catch (Exception e) {
                                log.debug("添加系统字体失败: {}", path);
                            }
                        });
            }
        } catch (Exception e) {
            log.debug("扫描字体目录失败: {}", dirPath);
        }
    }




    /**
     * 确定字体路径
     */
    private static String determineFontPath(PdfRenderOptions options) {
        // 1. 优先使用配置的外部字体路径
        if (!Str.isEmpty(options.getExternalFontPath())) {
            return options.getExternalFontPath();
        }

        // 2. 使用配置的字体路径
        if (!Str.isEmpty(options.getFontPath())) {
            return options.getFontPath();
        }

        // 3. 使用环境变量
        String envFontPath = System.getenv("FONT_PATH");
        if (!Str.isEmpty(envFontPath)) {
            return envFontPath;
        }

        // 4. 使用系统属性
        String sysFontPath = System.getProperty("font.path");
        if (!Str.isEmpty(sysFontPath)) {
            return sysFontPath;
        }

        // 5. 使用默认路径
        return "/app/fonts";
    }



    /**
     * 从文件生成PDF
     */
    public static void generatePdfFromFile(String htmlFilePath, String outputFilePath, PdfRenderOptions options) {
        try {
            String htmlContent = Files.readString(Paths.get(htmlFilePath));

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                Path htmlPath = Paths.get(htmlFilePath);
                String baseUrl = htmlPath.getParent().toUri().toString();

                // 创建新的options对象
                options = PdfRenderOptions.builder()
                        .marginLeft(options.getMarginLeft())
                        .marginRight(options.getMarginRight())
                        .marginTop(options.getMarginTop())
                        .marginBottom(options.getMarginBottom())
                        .defaultFontFamily(options.getDefaultFontFamily())
                        .defaultFontSize(options.getDefaultFontSize())
                        .fontPath(options.getFontPath())
                        .watermarkOptions(options.getWatermarkOptions())
                        .headerFooterOptions(options.getHeaderFooterOptions())
                        .customCssProperties(options.getCustomCssProperties())
                        .baseUrl(baseUrl)
                        .build();
            }

            try (FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {
                generatePdf(htmlContent, outputStream, options);
            }

        } catch (Exception e) {
            throw new PdfException("从文件生成PDF失败", e);
        }
    }

    /**
     * 生成PDF到字节数组
     */
    public static byte[] generatePdfToBytes(String htmlContent, PdfRenderOptions options) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generatePdf(htmlContent, outputStream, options);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new PdfException("生成PDF字节数组失败", e);
        }
    }

    /**
     * 生成PDF到字节数组（从文件）
     */
    public static byte[] generatePdfFromFileToBytes(String htmlFilePath, PdfRenderOptions options) {
        try {
            String htmlContent = Files.readString(Paths.get(htmlFilePath));

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                Path htmlPath = Paths.get(htmlFilePath);
                String baseUrl = htmlPath.getParent().toUri().toString();

                // 创建新的options对象
                options = PdfRenderOptions.builder()
                        .marginLeft(options.getMarginLeft())
                        .marginRight(options.getMarginRight())
                        .marginTop(options.getMarginTop())
                        .marginBottom(options.getMarginBottom())
                        .defaultFontFamily(options.getDefaultFontFamily())
                        .defaultFontSize(options.getDefaultFontSize())
                        .fontPath(options.getFontPath())
                        .watermarkOptions(options.getWatermarkOptions())
                        .headerFooterOptions(options.getHeaderFooterOptions())
                        .customCssProperties(options.getCustomCssProperties())
                        .baseUrl(baseUrl)
                        .build();
            }

            return generatePdfToBytes(htmlContent, options);
        } catch (Exception e) {
            throw new PdfException("从文件生成PDF字节数组失败", e);
        }
    }
}


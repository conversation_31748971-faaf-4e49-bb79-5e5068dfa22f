package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.openpdf.pdf.ITextRenderer;

import java.awt.Color;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * HTML转PDF生成器
 * 支持水印、页眉页脚、外部资源
 */
@Slf4j
public class HtmlToPdfGenerator {

    // 字体缓存，避免重复加载
    private static final Map<String, String> fontCache = new ConcurrentHashMap<>();
    private static volatile boolean fontsInitialized = false;
    /**
     * 将HTML转换为PDF
     */
    public static void generatePdf(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        if (Str.isEmpty(htmlContent)) {
            throw new PdfException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new PdfException("输出流不能为空");
        }

        if (options == null) {
            options = new PdfRenderOptions();
        }

        // 自动设置baseUrl
        if (Str.isEmpty(options.getBaseUrl())) {
            ensureBaseUrl(options);
        }

        try {
            // 如果需要水印或页眉页脚，使用传统PDF方式
            if (hasWatermarkOrHeaderFooter(options)) {
                generatePdfWithEvents(htmlContent, outputStream, options);
            } else {
                // 纯HTML转PDF，使用HTML渲染器
                generatePdfWithRenderer(htmlContent, outputStream, options);
            }
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new PdfException("生成PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从文件生成PDF
     */
    public static void generatePdfFromFile(String htmlFilePath, String outputFilePath, PdfRenderOptions options) {
        try {
            Path path = Paths.get(htmlFilePath);
            String htmlContent = Files.readString(path);

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                String baseUrl = path.getParent().toUri().toString();
                options.setBaseUrl(baseUrl);
            }

            try (FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {
                generatePdf(htmlContent, outputStream, options);
            }

        } catch (Exception e) {
            throw new PdfException("从文件生成PDF失败", e);
        }
    }

    /**
     * 生成PDF到字节数组
     */
    public static byte[] generatePdfToBytes(String htmlContent, PdfRenderOptions options) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generatePdf(htmlContent, outputStream, options);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new PdfException("生成PDF字节数组失败", e);
        }
    }

    /**
     * 生成PDF到字节数组（从文件）
     */
    public static byte[] generatePdfFromFileToBytes(String htmlFilePath, PdfRenderOptions options) {
        try {
            Path path = Paths.get(htmlFilePath);
            String htmlContent = Files.readString(path);

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                String baseUrl = path.getParent().toUri().toString();
                options.setBaseUrl(baseUrl);
            }

            return generatePdfToBytes(htmlContent, options);
        } catch (Exception e) {
            throw new PdfException("从文件生成PDF字节数组失败", e);
        }
    }

    /**
     * 预处理HTML，注入页边距和自定义CSS
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        StringBuilder cssBuilder = new StringBuilder();

        // 添加页面设置CSS
        cssBuilder.append("@page { ").append("margin-top: ").append(options.getMarginTop()).append("pt; ").append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ").append("margin-left: ").append(options.getMarginLeft()).append("pt; ").append("margin-right: ").append(options.getMarginRight()).append("pt; ");

        // 添加页面大小设置
        if (options.getPageSize() != null) {
            cssBuilder.append("size: ").append(getPageSizeCss(options.getPageSize())).append("; ");
        }

        cssBuilder.append("} ");

        // 添加CJK字体CSS
        String fontFamily = options.getDefaultFontFamily();
        String normalizedFontFamily = normalizeForRenderer(fontFamily);

        cssBuilder.append("body { ")
                .append("font-family: '").append(normalizedFontFamily).append("', ")
                .append("'").append(fontFamily).append("', ")
                .append("'MSung-Light-H', 'STSong-Light-H', 'HeiseiMin-W3-H', ")
                .append("SansSerif, sans-serif; ")
                .append("font-size: ").append(options.getDefaultFontSize()).append("pt; ")
                .append("} ")
                .append("* { ")
                .append("font-family: inherit; ")
                .append("} ");

        // 添加自定义CSS属性
        if (options.getCustomCssProperties() != null) {
            options.getCustomCssProperties().forEach((selector, rules) -> cssBuilder.append(selector).append(" { ").append(rules).append(" } "));
        }

        String cssContent = cssBuilder.toString();

        // 如果HTML中没有head标签，添加一个
        if (!htmlContent.toLowerCase().contains("<head>")) {
            if (htmlContent.toLowerCase().contains("<html>")) {
                htmlContent = htmlContent.replaceFirst("(?i)<html[^>]*>", "$0<head><style>" + cssContent + "</style></head>");
            } else {
                htmlContent = "<html><head><style>" + cssContent + "</style></head><body>" + htmlContent + "</body></html>";
            }
        } else {
            // 在head标签中添加CSS
            htmlContent = htmlContent.replaceFirst("(?i)</head>", "<style>" + cssContent + "</style></head>");
        }

        return htmlContent;
    }

    /**
     * 自动设置baseUrl
     */
    private static PdfRenderOptions ensureBaseUrl(PdfRenderOptions options) {
        try {
            var request = ServletUtil.getRequest();
            if (request != null) {
                String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
                options.setBaseUrl(baseUrl);
            }
        } catch (Exception e) {
            log.debug("无法获取当前请求，跳过自动设置baseUrl", e);
        }

        return options;
    }

    /**
     * 检查是否需要水印或页眉页脚
     */
    private static boolean hasWatermarkOrHeaderFooter(PdfRenderOptions options) {
        return (options.getWatermarkOptions() != null && (!Str.isEmpty(options.getWatermarkOptions().getText()) || !Str.isEmpty(options.getWatermarkOptions().getImagePath()))) || (options.getHeaderFooterOptions() != null);
    }

    /**
     * 使用事件处理器生成PDF（支持水印和页眉页脚）
     */
    private static void generatePdfWithEvents(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        try (ByteArrayOutputStream tempStream = new ByteArrayOutputStream()) {
            // 先生成基础HTML到PDF
            generatePdfWithRenderer(htmlContent, tempStream, options);

            // 获取PDF字节数据再添加水印或页眉页脚
            byte[] pdfBytes = tempStream.toByteArray();
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);
                 PdfReader reader = new PdfReader(inputStream);
                 PdfStamper stamper = new PdfStamper(reader, outputStream)) {

                // 添加水印和页眉页脚
                addWatermarkAndHeaderFooter(stamper, options);
            }

        } catch (Exception e) {
            throw new PdfException("生成带事件PDF失败", e);
        }
    }

    /**
     * 使用HTML渲染器生成PDF（纯HTML转换）
     */
    private static void generatePdfWithRenderer(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        ITextRenderer renderer = null;
        try {
            // HTML渲染器的页面大小主要通过CSS @page规则控制
            renderer = new ITextRenderer();

            // 设置渲染选项
            renderer.getSharedContext().setPrint(true);
            renderer.getSharedContext().setInteractive(false);

            // 设置字体
            setupFonts(renderer, options);

            // 处理HTML内容
            String processedHtml = preprocessHtml(htmlContent, options);

            // 设置文档
            if (!Str.isEmpty(options.getBaseUrl())) {
                renderer.setDocumentFromString(processedHtml, options.getBaseUrl());
            } else {
                renderer.setDocumentFromString(processedHtml);
            }

            renderer.layout();
            renderer.createPDF(outputStream);

        } catch (Exception e) {
            throw new PdfException("HTML-PDF渲染失败", e);
        } finally {
            // 确保资源清理
            if (renderer != null) {
                try {
                    renderer.finishPDF();
                } catch (Exception e) {
                    log.debug("清理HTML渲染器资源时出现异常", e);
                }
            }
        }
    }

    /**
     * 添加水印和页眉页脚
     */
    private static void addWatermarkAndHeaderFooter(PdfStamper stamper, PdfRenderOptions options) {
        try {
            int totalPages = stamper.getReader().getNumberOfPages();

            for (int i = 1; i <= totalPages; i++) {
                PdfContentByte canvas = stamper.getOverContent(i);
                Rectangle pageSize = stamper.getReader().getPageSize(i);

                // 添加水印
                if (options.getWatermarkOptions() != null) {
                    WatermarkProcessor.addWatermark(canvas, pageSize, options.getWatermarkOptions());
                }

                // 添加页眉页脚
                if (options.getHeaderFooterOptions() != null) {
                    addHeaderFooter(canvas, pageSize, options, i, totalPages);
                }
            }
        } catch (Exception e) {
            log.error("添加水印和页眉页脚失败", e);
        }
    }

    /**
     * 添加页眉页脚
     */
    private static void addHeaderFooter(PdfContentByte canvas, Rectangle pageSize,
                                        PdfRenderOptions options,
                                        int currentPage, int totalPages) {
        try {
            var config = options.getHeaderFooterOptions();

            // 创建默认字体
            BaseFont baseFont = getDefaultFont(options);
            Font font = new Font(baseFont, config.getFontSize());

            float headerY = pageSize.getTop() - 20;
            float footerY = pageSize.getBottom() + 20;

            // 页眉
            if (!Str.isEmpty(config.getHeaderLeft())) {
                addColoredText(canvas, config.getHeaderLeft(), 50, headerY, Element.ALIGN_LEFT, font, config.getHeaderLeftColor());
            }
            if (!Str.isEmpty(config.getHeaderCenter())) {
                addColoredText(canvas, config.getHeaderCenter(), pageSize.getWidth() / 2, headerY, Element.ALIGN_CENTER, font, config.getHeaderCenterColor());
            }
            if (!Str.isEmpty(config.getHeaderRight())) {
                addColoredText(canvas, config.getHeaderRight(), pageSize.getWidth() - 50, headerY, Element.ALIGN_RIGHT, font, config.getHeaderRightColor());
            }

            // 页脚
            if (!Str.isEmpty(config.getFooterLeft())) {
                String text = processPageNumberTemplate(config.getFooterLeft(), currentPage, totalPages);
                addColoredText(canvas, text, 50, footerY, Element.ALIGN_LEFT, font, config.getFooterLeftColor());
            }
            if (!Str.isEmpty(config.getFooterCenter())) {
                String text = processPageNumberTemplate(config.getFooterCenter(), currentPage, totalPages);
                addColoredText(canvas, text, pageSize.getWidth() / 2, footerY, Element.ALIGN_CENTER, font, config.getFooterCenterColor());
            }
            if (!Str.isEmpty(config.getFooterRight())) {
                String text = processPageNumberTemplate(config.getFooterRight(), currentPage, totalPages);
                addColoredText(canvas, text, pageSize.getWidth() - 50, footerY, Element.ALIGN_RIGHT, font, config.getFooterRightColor());
            }

            // 默认页码
            if (config.isShowPageNumbers() && Str.isEmpty(config.getFooterLeft()) && Str.isEmpty(config.getFooterCenter()) && Str.isEmpty(config.getFooterRight())) {
                String pageText = processPageNumberTemplate(config.getPageNumberFormat(), currentPage, totalPages);
                addColoredText(canvas, pageText, pageSize.getWidth() / 2, footerY, Element.ALIGN_CENTER, font, "#000000");
            }

        } catch (Exception e) {
            log.error("添加页眉页脚失败", e);
        }
    }

    /**
     * 添加带颜色的文本
     */
    private static void addColoredText(PdfContentByte canvas, String text, float x, float y, int alignment, Font font, String color) {
        try {
            // 解析颜色
            Color textColor = parseColor(color);

            // 创建带颜色的字体
            Font coloredFont = new Font(font.getBaseFont(), font.getSize(), font.getStyle(), textColor);

            Phrase phrase = new Phrase(text, coloredFont);
            ColumnText.showTextAligned(canvas, alignment, phrase, x, y, 0);
        } catch (Exception e) {
            log.error("添加带颜色文本失败", e);
        }
    }

    /**
     * 解析颜色字符串
     */
    private static Color parseColor(String colorStr) {
        try {
            if (Str.isEmpty(colorStr)) {
                return Color.BLACK;
            }

            if (colorStr.startsWith("#")) {
                colorStr = colorStr.substring(1);
            }

            if (colorStr.length() == 6) {
                int r = Integer.parseInt(colorStr.substring(0, 2), 16);
                int g = Integer.parseInt(colorStr.substring(2, 4), 16);
                int b = Integer.parseInt(colorStr.substring(4, 6), 16);
                return new Color(r, g, b);
            }

            return Color.BLACK;
        } catch (Exception e) {
            log.warn("解析颜色失败: {}，将采用默认黑色", colorStr);
            return Color.BLACK;
        }
    }

    /**
     * 获取页面大小的CSS值
     */
    private static String getPageSizeCss(PageSize pageSize) {
        return switch (pageSize) {
            case A3 -> "A3";
            case A5 -> "A5";
            case LETTER -> "letter";
            case LEGAL -> "legal";
            default -> "A4";
        };
    }

    /**
     * 处理页码模板
     */
    private static String processPageNumberTemplate(String template, int currentPage, int totalPages) {
        if (Str.isEmpty(template)) {
            return "";
        }
        return template.replace("{page}", String.valueOf(currentPage)).replace("{total}", String.valueOf(totalPages));
    }

    /**
     * 设置字体（基于CJKFontResolver的正确方式）
     */
    private static void setupFonts(ITextRenderer renderer, PdfRenderOptions options) {
        try {
            // 1. 使用CJK字体解析器
            setupCJKFonts(renderer);

            // 2. 注册自定义中文字体
            initializeFonts(options);

            int successCount = 0;
            for (Map.Entry<String, String> entry : fontCache.entrySet()) {
                String fontPath = entry.getValue();
                String originalFontFamily = entry.getKey();

                if (Files.exists(Paths.get(fontPath))) {
                    try {
                        // 使用标准化的字体族名称注册
                        String normalizedFontFamily = normalizeForRenderer(originalFontFamily);
                        renderer.getFontResolver().addFont(fontPath, normalizedFontFamily, true);
                        successCount++;
                        log.debug("自定义字体注册成功: {} (标准化为: {}) -> {}", originalFontFamily, normalizedFontFamily, fontPath);
                    } catch (Exception e) {
                        log.debug("自定义字体注册失败: {} -> {}", originalFontFamily, fontPath, e);
                    }
                }
            }

            // 3. 注册外部字体目录
            String externalFontPath = determineFontPath(options);
            if (!Str.isEmpty(externalFontPath)) {
                try {
                    renderer.getFontResolver().addFontDirectory(externalFontPath, true);
                    log.info("外部字体目录注册成功: {}", externalFontPath);
                } catch (Exception e) {
                    log.debug("外部字体目录注册失败: {}", externalFontPath, e);
                }
            }

            log.info("字体设置完成，CJK字体: 已加载, 自定义字体: {}", successCount);

        } catch (Exception e) {
            log.warn("设置字体失败", e);
        }
    }

    /**
     * 设置CJK字体支持
     */
    private static void setupCJKFonts(ITextRenderer renderer) {
        try {
            // 根据官方测试案例，注册已知的CJK字体
            String[] cjkFontNames = {
                // 中文字体
                "MSung-Light-H", "MSung-Medium-H", "MSung-DemiBold-H",
                "MHei-Medium-H", "MKai-Medium-H",
                "STSong-Light-H", "STHeiti-Regular-H",

                // 日文字体
                "HeiseiMin-W3-H", "HeiseiKakuGo-W5-H",
                "KozMinPro-Regular-H", "KozGoPro-Medium-H",

                // 韩文字体
                "HYGoThic-Medium-H", "HYSMyeongJoStd-Medium-H",

                // 基础字体
                "Helvetica", "Courier", "TimesRoman", "Serif"
            };

            int cjkSuccessCount = 0;
            for (String fontName : cjkFontNames) {
                try {
                    renderer.getFontResolver().addFont(fontName, true);
                    cjkSuccessCount++;
                    log.debug("CJK字体注册成功: {}", fontName);
                } catch (Exception e) {
                    log.debug("CJK字体注册失败: {}", fontName, e);
                }
            }

            log.info("CJK字体设置完成，成功注册 {}/{} 个CJK字体", cjkSuccessCount, cjkFontNames.length);

        } catch (Exception e) {
            log.warn("CJK字体设置失败", e);
        }
    }

    /**
     * 为渲染器标准化字体族名称
     */
    private static String normalizeForRenderer(String fontFamily) {
        if (fontFamily == null) {
            return "SansSerif";
        }

        String normalized = fontFamily.toLowerCase().trim();

        // 根据ITextFontResolver的标准化规则
        switch (normalized) {
            case "serif":
                return "Serif";
            case "sans-serif":
            case "sans serif":
                return "SansSerif";
            case "monospace":
            case "mono":
                return "Monospaced";
            case "simhei":
            case "SimHei":
                return "SimHei";
            case "simsun":
            case "SimSun":
                return "SimSun";
            default:
                // 保持原始名称，但首字母大写
                return Character.toUpperCase(fontFamily.charAt(0)) + fontFamily.substring(1).toLowerCase();
        }
    }





    /**
     * 初始化字体缓存（只执行一次）
     */
    private static void initializeFonts(PdfRenderOptions options) {
        if (fontsInitialized) {
            return;
        }

        synchronized (HtmlToPdfGenerator.class) {
            if (fontsInitialized) {
                return;
            }

            try {
                // 1. 扫描资源字体
                scanResourceFonts();

                // 2. 如果配置了外部字体目录，扫描外部字体
                String externalFontPath = determineFontPath(options);
                if (!Str.isEmpty(externalFontPath)) {
                    scanExternalFonts(externalFontPath);
                }

                fontsInitialized = true;
                log.info("字体初始化完成，发现 {} 个字体", fontCache.size());

            } catch (Exception e) {
                log.warn("字体初始化失败", e);
            }
        }
    }

    /**
     * 扫描资源字体
     */
    private static void scanResourceFonts() {
        try {
            // 从classpath扫描fonts目录
            InputStream resourceStream = HtmlToPdfGenerator.class.getClassLoader()
                    .getResourceAsStream("fonts");

            if (resourceStream != null) {
                resourceStream.close();
                // 如果fonts目录存在，扫描其中的字体文件
                scanResourceFontDirectory();
            } else {
                log.debug("未找到resources/fonts目录");
            }

        } catch (Exception e) {
            log.debug("扫描资源字体失败", e);
        }
    }

    /**
     * 扫描资源字体目录（简化版）
     */
    private static void scanResourceFontDirectory() {
        // 直接扫描已知的字体文件
        String[] knownFonts = {
            "simhei.ttf", "SimHei.ttf", "SIMHEI.ttf",
            "simsun.ttc", "SimSun.ttf", "SIMSUN.ttf",
            "simsun.ttf", "SimSun.ttc", "SIMSUN.ttc"
        };

        for (String fontFile : knownFonts) {
            try {
                InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                        .getResourceAsStream("fonts/" + fontFile);

                if (fontStream != null) {
                    fontStream.close();

                    // 提取字体到临时文件
                    String tempFontPath = extractResourceFont(fontFile);
                    if (tempFontPath != null) {
                        // 使用标准化的字体族名称
                        String fontFamily = normalizeFontFamily(fontFile);
                        fontCache.put(fontFamily, tempFontPath);
                        log.debug("发现资源字体: {} -> {}", fontFamily, fontFile);
                    }
                }
            } catch (Exception e) {
                log.debug("检查资源字体失败: {}", fontFile, e);
            }
        }
    }

    /**
     * 标准化字体族名称
     */
    private static String normalizeFontFamily(String fontFile) {
        String name = fontFile.toLowerCase();
        if (name.contains("simhei")) {
            return "simhei";
        } else if (name.contains("simsun")) {
            return "simsun";
        } else {
            return extractFontFamily(fontFile);
        }
    }

    /**
     * 提取资源字体到临时文件
     */
    private static String extractResourceFont(String fontFile) {
        try {
            InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                    .getResourceAsStream("fonts/" + fontFile);

            if (fontStream == null) {
                log.debug("资源字体文件不存在: fonts/{}", fontFile);
                return null;
            }

            // 读取字体数据
            byte[] fontData = fontStream.readAllBytes();
            fontStream.close();

            // 创建临时文件（使用简单的文件名）
            String tempDir = System.getProperty("java.io.tmpdir");
            String tempFontPath = tempDir + File.separator + fontFile;

            File tempFile = new File(tempFontPath);

            // 如果文件已存在且大小正确，直接返回
            if (tempFile.exists() && tempFile.length() == fontData.length) {
                log.debug("字体文件已存在: {} ({}字节)", tempFontPath, tempFile.length());
                return tempFontPath;
            }

            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(fontData);
                fos.flush();
            }

            // 验证文件是否创建成功
            if (tempFile.exists() && tempFile.length() > 0) {
                log.debug("字体文件提取成功: {} -> {} ({}字节)", fontFile, tempFontPath, tempFile.length());
                return tempFontPath;
            } else {
                log.warn("字体文件提取失败，文件为空: {}", tempFontPath);
                return null;
            }

        } catch (Exception e) {
            log.warn("提取资源字体失败: {}", fontFile, e);
            return null;
        }
    }

    /**
     * 扫描外部字体目录
     */
    private static void scanExternalFonts(String fontPath) {
        try {
            Path fontDir = Paths.get(fontPath);
            if (!Files.exists(fontDir) || !Files.isDirectory(fontDir)) {
                log.warn("外部字体目录不存在: {}", fontPath);
                return;
            }

            try (var files = Files.walk(fontDir)) {
                files.filter(path -> {
                    String fileName = path.toString().toLowerCase();
                    return fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc");
                }).forEach(path -> {
                    String fontFamily = extractFontFamily(path.getFileName().toString());
                    fontCache.put(fontFamily, path.toString());
                    log.debug("发现外部字体: {} -> {}", fontFamily, path);
                });
            }

        } catch (Exception e) {
            log.warn("扫描外部字体目录失败: {}", fontPath, e);
        }
    }



    /**
     * 从字体文件名提取字体族名称
     */
    private static String extractFontFamily(String fontFileName) {
        String name = fontFileName;
        int dotIndex = name.lastIndexOf('.');
        if (dotIndex > 0) {
            name = name.substring(0, dotIndex);
        }
        return name.toLowerCase(); // 统一使用小写
    }

    /**
     * 确定字体路径
     */
    private static String determineFontPath(PdfRenderOptions options) {
        // 优先使用环境变量
        String envFontPath = System.getenv("FONT_PATH");
        if (!Str.isEmpty(envFontPath)) {
            return envFontPath;
        }

        // 使用配置的字体路径
        if (!Str.isEmpty(options.getFontPath())) {
            return options.getFontPath();
        }

        return null;
    }

    /**
     * 获取配置的默认字体（使用统一字体缓存）
     */
    private static BaseFont getDefaultFont(PdfRenderOptions options) throws IOException {
        try {
            // 初始化字体缓存
            initializeFonts(options);

            String fontFamily = options.getDefaultFontFamily().toLowerCase();

            // 从字体缓存中查找
            String fontPath = fontCache.get(fontFamily);
            if (fontPath != null) {
                // 验证字体文件是否存在
                if (Files.exists(Paths.get(fontPath))) {
                    try {
                        BaseFont baseFont = BaseFont.createFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                        log.debug("使用缓存字体: {} -> {}", fontFamily, fontPath);
                        return baseFont;
                    } catch (Exception e) {
                        log.warn("创建字体失败: {} -> {}, 错误: {}", fontFamily, fontPath, e.getMessage());
                    }
                } else {
                    log.warn("字体文件不存在: {} -> {}", fontFamily, fontPath);
                }
            }

            log.warn("未找到可用的配置字体: {}, 可用字体: {}", fontFamily, fontCache.keySet());

        } catch (Exception e) {
            log.warn("加载字体失败: {}, 使用默认字体", options.getDefaultFontFamily(), e);
        }

        // 使用系统默认字体
        return BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.EMBEDDED);
    }



    /**
     * 安全关闭资源的工具方法
     */
    private static void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                log.debug("关闭资源时出现异常", e);
            }
        }
    }

    /**
     * 安全关闭PdfStamper
     */
    private static void closeQuietly(PdfStamper stamper) {
        if (stamper != null) {
            try {
                stamper.close();
            } catch (Exception e) {
                log.debug("关闭PdfStamper时出现异常", e);
            }
        }
    }

    /**
     * 安全关闭PdfReader
     */
    private static void closeQuietly(PdfReader reader) {
        if (reader != null) {
            try {
                reader.close();
            } catch (Exception e) {
                log.debug("关闭PdfReader时出现异常", e);
            }
        }
    }
}


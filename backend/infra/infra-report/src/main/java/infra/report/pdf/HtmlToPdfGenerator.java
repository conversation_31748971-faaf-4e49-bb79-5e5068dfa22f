package infra.report.pdf;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.slf4j.Slf4jLogger;
import com.openhtmltopdf.util.XRLog;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

import java.awt.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于OpenHtmlToPdf的HTML转PDF生成器
 *
 * <AUTHOR> Agent
 */
@Slf4j
public class HtmlToPdfGenerator {

    // 字体缓存
    private static final Map<String, String> fontCache = new ConcurrentHashMap<>();

    // 字体初始化标志
    private static volatile boolean fontsInitialized = false;

    // 双重检查锁定
    private static final Object fontLock = new Object();

    /**
     * 生成PDF
     */
    public static byte[] generatePdf(String htmlContent, PdfRenderOptions options) {
        try {
            // 配置日志
            setupLogging();

            // 预处理HTML
            String processedHtml = preprocessHtml(htmlContent, options);

            // 初始化字体
            initializeFonts(options);

            // 生成PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            PdfRendererBuilder builder = new PdfRendererBuilder();

            // 设置HTML内容
            builder.withHtmlContent(processedHtml, getBaseUri());

            // 配置输出流
            builder.toStream(outputStream);

            // 设置字体
            setupFonts(builder, options);

            // 设置页面配置
            setupPageConfiguration(builder, options);

            // 运行渲染
            builder.run();

            byte[] pdfBytes = outputStream.toByteArray();

            // 添加页眉页脚和水印
            return addHeaderFooterAndWatermark(pdfBytes, options);

        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw new RuntimeException("PDF生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 配置日志
     */
    private static void setupLogging() {
        // 使用SLF4J日志
        XRLog.setLoggerImpl(new Slf4jLogger());

        // 设置日志级别（可选）
        XRLog.setLoggingEnabled(true);
    }

    /**
     * 预处理HTML，注入CSS和配置
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        StringBuilder cssBuilder = new StringBuilder();

        // 添加页面设置CSS
        cssBuilder.append("@page { ");
        cssBuilder.append("margin-top: ").append(options.getMarginTop()).append("pt; ");
        cssBuilder.append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ");
        cssBuilder.append("margin-left: ").append(options.getMarginLeft()).append("pt; ");
        cssBuilder.append("margin-right: ").append(options.getMarginRight()).append("pt; ");

        // 添加页面大小设置
        if (options.getPageSize() != null) {
            cssBuilder.append("size: ").append(getPageSizeCss(options.getPageSize())).append("; ");
        }

        cssBuilder.append("} ");

        // 添加字体CSS
        String fontFamily = options.getDefaultFontFamily();
        cssBuilder.append("body { ");
        cssBuilder.append("font-family: '").append(fontFamily).append("', ");
        cssBuilder.append("'SimHei', 'Microsoft YaHei', 'NotoSansCJK-Regular', sans-serif; ");
        cssBuilder.append("font-size: ").append(options.getDefaultFontSize()).append("pt; ");
        cssBuilder.append("} ");

        // 添加自定义CSS
        if (options.getCustomCssProperties() != null) {
            options.getCustomCssProperties().forEach((selector, rules) ->
                cssBuilder.append(selector).append(" { ").append(rules).append(" } "));
        }

        String cssContent = cssBuilder.toString();

        // 注入CSS到HTML
        if (!htmlContent.toLowerCase().contains("<head>")) {
            if (htmlContent.toLowerCase().contains("<html>")) {
                htmlContent = htmlContent.replaceFirst("(?i)<html[^>]*>",
                    "$0<head><style>" + cssContent + "</style></head>");
            } else {
                htmlContent = "<html><head><style>" + cssContent + "</style></head><body>"
                    + htmlContent + "</body></html>";
            }
        } else {
            htmlContent = htmlContent.replaceFirst("(?i)<head>",
                "$0<style>" + cssContent + "</style>");
        }

        return htmlContent;
    }

    /**
     * 获取基础URI
     */
    private static String getBaseUri() {
        try {
            return ServletUtil.getRequest().getRequestURL().toString();
        } catch (Exception e) {
            return "file:///";
        }
    }

    /**
     * 获取页面大小CSS
     */
    private static String getPageSizeCss(PdfRenderOptions.PageSize pageSize) {
        return switch (pageSize) {
            case A4 -> "A4";
            case A3 -> "A3";
            case A5 -> "A5";
            case LETTER -> "letter";
            case LEGAL -> "legal";
        };
    }

    /**
     * 初始化字体
     */
    private static void initializeFonts(PdfRenderOptions options) {
        if (!fontsInitialized) {
            synchronized (fontLock) {
                if (!fontsInitialized) {
                    try {
                        // 扫描资源字体
                        scanResourceFonts();

                        // 扫描外部字体
                        String externalFontPath = determineFontPath(options);
                        if (!Str.isEmpty(externalFontPath)) {
                            scanExternalFonts(externalFontPath);
                        }

                        fontsInitialized = true;
                        log.info("字体初始化完成，发现 {} 个字体", fontCache.size());

                    } catch (Exception e) {
                        log.warn("字体初始化失败", e);
                    }
                }
            }
        }
    }

    /**
     * 扫描资源字体
     */
    private static void scanResourceFonts() {
        String[] knownFonts = {
            "simhei.ttf", "SimHei.ttf",
            "simsun.ttc", "SimSun.ttf", "simsun.ttf",
            "NotoSansCJK-Regular.ttc", "SourceHanSans-Regular.ttc"
        };

        for (String fontFile : knownFonts) {
            try {
                InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                        .getResourceAsStream("fonts/" + fontFile);

                if (fontStream != null) {
                    fontStream.close();

                    String fontFamily = extractFontFamily(fontFile);
                    fontCache.put(fontFamily, "classpath:fonts/" + fontFile);
                    log.debug("发现资源字体: {} -> {}", fontFamily, fontFile);
                }
            } catch (Exception e) {
                log.debug("检查资源字体失败: {}", fontFile, e);
            }
        }
    }

    /**
     * 扫描外部字体
     */
    private static void scanExternalFonts(String fontPath) {
        try {
            Path fontDir = Paths.get(fontPath);
            if (Files.exists(fontDir) && Files.isDirectory(fontDir)) {
                Files.walk(fontDir)
                    .filter(path -> {
                        String fileName = path.getFileName().toString().toLowerCase();
                        return fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc");
                    })
                    .forEach(path -> {
                        String fontFamily = extractFontFamily(path.getFileName().toString());
                        fontCache.put(fontFamily, path.toString());
                        log.debug("发现外部字体: {} -> {}", fontFamily, path);
                    });
            }
        } catch (Exception e) {
            log.warn("扫描外部字体失败: {}", fontPath, e);
        }
    }

    /**
     * 提取字体族名称
     */
    private static String extractFontFamily(String fontFile) {
        String name = fontFile.toLowerCase();
        if (name.contains("simhei")) {
            return "simhei";
        } else if (name.contains("simsun")) {
            return "simsun";
        } else if (name.contains("noto")) {
            return "noto";
        } else if (name.contains("source")) {
            return "source";
        } else {
            return name.replaceAll("\\.(ttf|otf|ttc)$", "");
        }
    }

    /**
     * 确定字体路径
     */
    private static String determineFontPath(PdfRenderOptions options) {
        if (!Str.isEmpty(options.getFontPath())) {
            return options.getFontPath();
        }

        String[] possiblePaths = {
            "/usr/share/fonts/",
            "/System/Library/Fonts/",
            "C:/Windows/Fonts/"
        };

        for (String path : possiblePaths) {
            if (Files.exists(Paths.get(path))) {
                return path;
            }
        }

        return null;
    }

    /**
     * 设置字体
     */
    private static void setupFonts(PdfRendererBuilder builder, PdfRenderOptions options) {
        try {
            // 注册字体
            for (Map.Entry<String, String> entry : fontCache.entrySet()) {
                String fontFamily = entry.getKey();
                String fontPath = entry.getValue();

                try {
                    if (fontPath.startsWith("classpath:")) {
                        // 资源字体
                        String resourcePath = fontPath.substring("classpath:".length());
                        InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                                .getResourceAsStream(resourcePath);
                        if (fontStream != null) {
                            // 使用最新API注册字体
                            builder.useFont(() -> fontStream, fontFamily);
                            log.debug("注册资源字体: {}", fontFamily);
                        }
                    } else {
                        // 外部字体文件
                        File fontFile = new File(fontPath);
                        if (fontFile.exists()) {
                            // 使用最新API注册字体
                            builder.useFont(fontFile, fontFamily);
                            log.debug("注册外部字体: {} -> {}", fontFamily, fontPath);
                        }
                    }
                } catch (Exception e) {
                    log.debug("字体注册失败: {} -> {}", fontFamily, fontPath, e);
                }
            }

            // 添加默认字体回退
            addDefaultFontFallbacks(builder);

            log.info("字体设置完成，注册了 {} 个字体", fontCache.size());

        } catch (Exception e) {
            log.warn("设置字体失败", e);
        }
    }

    /**
     * 添加默认字体回退
     */
    private static void addDefaultFontFallbacks(PdfRendererBuilder builder) {
        try {
            // 添加系统默认字体作为回退
            builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM); // A4

            // 设置字体回退
            builder.useFastMode();

            log.debug("添加默认字体回退完成");

        } catch (Exception e) {
            log.debug("添加默认字体回退失败", e);
        }
    }

    /**
     * 设置页面配置
     */
    private static void setupPageConfiguration(PdfRendererBuilder builder, PdfRenderOptions options) {
        try {
            // 设置页面大小
            if (options.getPageSize() != null) {
                switch (options.getPageSize()) {
                    case A4 -> builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM);
                    case A3 -> builder.useDefaultPageSize(297, 420, PdfRendererBuilder.PageSizeUnits.MM);
                    case A5 -> builder.useDefaultPageSize(148, 210, PdfRendererBuilder.PageSizeUnits.MM);
                    case LETTER -> builder.useDefaultPageSize(8.5f, 11f, PdfRendererBuilder.PageSizeUnits.INCHES);
                    case LEGAL -> builder.useDefaultPageSize(8.5f, 14f, PdfRendererBuilder.PageSizeUnits.INCHES);
                }
            } else {
                // 默认A4
                builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM);
            }

            // 启用快速模式以提高性能
            builder.useFastMode();

            // 设置PDF版本
            builder.usePdfVersion(1.7f);

            // 启用可访问性支持
            builder.usePdfAConformance(PdfRendererBuilder.PdfAConformance.NONE);

            log.debug("页面配置设置完成");

        } catch (Exception e) {
            log.warn("设置页面配置失败", e);
        }
    }

    /**
     * 添加页眉页脚和水印
     */
    private static byte[] addHeaderFooterAndWatermark(byte[] pdfBytes, PdfRenderOptions options) {
        try (PDDocument document = PDDocument.load(pdfBytes);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 加载字体用于页眉页脚
            PDFont font = loadDefaultFont(document, options);

            int totalPages = document.getNumberOfPages();

            for (int i = 0; i < totalPages; i++) {
                PDPage page = document.getPage(i);

                try (PDPageContentStream contentStream = new PDPageContentStream(document, page,
                        PDPageContentStream.AppendMode.APPEND, true, true)) {

                    // 添加页眉
                    if (options.getHeaderFooterOptions() != null) {
                        addHeader(contentStream, page, font, options.getHeaderFooterOptions(), i + 1, totalPages);
                        addFooter(contentStream, page, font, options.getHeaderFooterOptions(), i + 1, totalPages);
                    }

                    // 添加水印
                    if (options.getWatermarkOptions() != null) {
                        addWatermark(contentStream, page, font, options.getWatermarkOptions());
                    }
                }
            }

            document.save(outputStream);
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.warn("添加页眉页脚和水印失败，返回原始PDF", e);
            return pdfBytes;
        }
    }

    /**
     * 加载默认字体
     */
    private static PDFont loadDefaultFont(PDDocument document, PdfRenderOptions options) {
        try {
            // 尝试加载中文字体
            String fontFamily = options.getDefaultFontFamily();
            String fontPath = fontCache.get(fontFamily);

            if (fontPath != null && !fontPath.startsWith("classpath:")) {
                File fontFile = new File(fontPath);
                if (fontFile.exists()) {
                    return PDType0Font.load(document, fontFile);
                }
            }

            // 尝试从资源加载
            if (fontPath != null && fontPath.startsWith("classpath:")) {
                String resourcePath = fontPath.substring("classpath:".length());
                InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader()
                        .getResourceAsStream(resourcePath);
                if (fontStream != null) {
                    return PDType0Font.load(document, fontStream);
                }
            }

        } catch (Exception e) {
            log.debug("加载自定义字体失败，使用默认字体", e);
        }

        // 使用默认字体
        try {
            return PDType0Font.load(document,
                HtmlToPdfGenerator.class.getClassLoader().getResourceAsStream("fonts/simhei.ttf"));
        } catch (Exception e) {
            log.debug("加载默认中文字体失败，使用系统字体", e);
            try {
                return org.apache.pdfbox.pdmodel.font.PDType1Font.HELVETICA;
            } catch (Exception e2) {
                log.error("无法加载任何字体", e2);
                throw new RuntimeException("无法加载字体", e2);
            }
        }
    }

    /**
     * 添加页眉
     */
    private static void addHeader(PDPageContentStream contentStream, PDPage page, PDFont font,
                                 PdfRenderOptions.HeaderFooterOptions options, int pageNumber, int totalPages) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float headerY = page.getMediaBox().getHeight() - 30; // 距离顶部30pt
            float fontSize = options.getFontSize();

            contentStream.setFont(font, fontSize);

            // 左侧页眉
            if (!Str.isEmpty(options.getHeaderLeft())) {
                String text = replacePlaceholders(options.getHeaderLeft(), pageNumber, totalPages);
                contentStream.setNonStrokingColor(parseColor(options.getHeaderLeftColor()));
                contentStream.beginText();
                contentStream.newLineAtOffset(50, headerY);
                contentStream.showText(text);
                contentStream.endText();
            }

            // 中间页眉
            if (!Str.isEmpty(options.getHeaderCenter())) {
                String text = replacePlaceholders(options.getHeaderCenter(), pageNumber, totalPages);
                contentStream.setNonStrokingColor(parseColor(options.getHeaderCenterColor()));
                float textWidth = font.getStringWidth(text) / 1000 * fontSize;
                contentStream.beginText();
                contentStream.newLineAtOffset((pageWidth - textWidth) / 2, headerY);
                contentStream.showText(text);
                contentStream.endText();
            }

            // 右侧页眉
            if (!Str.isEmpty(options.getHeaderRight())) {
                String text = replacePlaceholders(options.getHeaderRight(), pageNumber, totalPages);
                contentStream.setNonStrokingColor(parseColor(options.getHeaderRightColor()));
                float textWidth = font.getStringWidth(text) / 1000 * fontSize;
                contentStream.beginText();
                contentStream.newLineAtOffset(pageWidth - textWidth - 50, headerY);
                contentStream.showText(text);
                contentStream.endText();
            }

        } catch (Exception e) {
            log.warn("添加页眉失败", e);
        }
    }

    /**
     * 添加页脚
     */
    private static void addFooter(PDPageContentStream contentStream, PDPage page, PDFont font,
                                 PdfRenderOptions.HeaderFooterOptions options, int pageNumber, int totalPages) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float footerY = 30; // 距离底部30pt
            float fontSize = options.getFontSize();

            contentStream.setFont(font, fontSize);

            // 左侧页脚
            if (!Str.isEmpty(options.getFooterLeft())) {
                String text = replacePlaceholders(options.getFooterLeft(), pageNumber, totalPages);
                contentStream.setNonStrokingColor(parseColor(options.getFooterLeftColor()));
                contentStream.beginText();
                contentStream.newLineAtOffset(50, footerY);
                contentStream.showText(text);
                contentStream.endText();
            }

            // 中间页脚
            if (!Str.isEmpty(options.getFooterCenter())) {
                String text = replacePlaceholders(options.getFooterCenter(), pageNumber, totalPages);
                contentStream.setNonStrokingColor(parseColor(options.getFooterCenterColor()));
                float textWidth = font.getStringWidth(text) / 1000 * fontSize;
                contentStream.beginText();
                contentStream.newLineAtOffset((pageWidth - textWidth) / 2, footerY);
                contentStream.showText(text);
                contentStream.endText();
            }

            // 右侧页脚
            if (!Str.isEmpty(options.getFooterRight())) {
                String text = replacePlaceholders(options.getFooterRight(), pageNumber, totalPages);
                contentStream.setNonStrokingColor(parseColor(options.getFooterRightColor()));
                float textWidth = font.getStringWidth(text) / 1000 * fontSize;
                contentStream.beginText();
                contentStream.newLineAtOffset(pageWidth - textWidth - 50, footerY);
                contentStream.showText(text);
                contentStream.endText();
            }

        } catch (Exception e) {
            log.warn("添加页脚失败", e);
        }
    }

    /**
     * 添加水印
     */
    private static void addWatermark(PDPageContentStream contentStream, PDPage page, PDFont font,
                                   PdfRenderOptions.WatermarkOptions options) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(options.getOpacity());
            contentStream.setGraphicsStateParameters(graphicsState);

            // 设置字体和颜色
            contentStream.setFont(font, options.getFontSize());
            contentStream.setNonStrokingColor(parseColor(options.getColor()));

            String text = options.getText();
            float textWidth = font.getStringWidth(text) / 1000 * options.getFontSize();

            // 计算水印位置（居中）
            float x = (pageWidth - textWidth) / 2;
            float y = pageHeight / 2;

            // 应用旋转
            contentStream.saveGraphicsState();
            contentStream.transform(Matrix.getRotateInstance(Math.toRadians(options.getRotation()), x, y));

            contentStream.beginText();
            contentStream.newLineAtOffset(x, y);
            contentStream.showText(text);
            contentStream.endText();

            contentStream.restoreGraphicsState();

        } catch (Exception e) {
            log.warn("添加水印失败", e);
        }
    }

    /**
     * 替换占位符
     */
    private static String replacePlaceholders(String text, int pageNumber, int totalPages) {
        if (text == null) return "";
        return text.replace("{page}", String.valueOf(pageNumber))
                  .replace("{total}", String.valueOf(totalPages));
    }

    /**
     * 解析颜色
     */
    private static Color parseColor(String colorStr) {
        if (Str.isEmpty(colorStr)) {
            return Color.BLACK;
        }

        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                // 支持常见颜色名称
                return switch (colorStr.toLowerCase()) {
                    case "black" -> Color.BLACK;
                    case "white" -> Color.WHITE;
                    case "red" -> Color.RED;
                    case "green" -> Color.GREEN;
                    case "blue" -> Color.BLUE;
                    case "gray", "grey" -> Color.GRAY;
                    default -> Color.BLACK;
                };
            }
        } catch (Exception e) {
            log.debug("解析颜色失败: {}, 使用默认黑色", colorStr);
            return Color.BLACK;
        }
    }
}
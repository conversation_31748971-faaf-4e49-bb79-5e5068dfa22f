package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.html.HtmlParser;
import com.lowagie.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * HTML转PDF生成器 - 基于OpenPDF 2.2.4
 */
@Slf4j
public class HtmlToPdfGenerator implements AutoCloseable {
    private final PdfRenderOptions options;
    private BaseFont chineseFont;

    /**
     * 创建HTML转PDF生成器
     * 
     * @param options PDF渲染选项
     */
    public HtmlToPdfGenerator(PdfRenderOptions options) {
        this.options = options != null ? options : new PdfRenderOptions();
        initializeChineseFont();
    }

    /**
     * 初始化中文字体
     */
    private void initializeChineseFont() {
        try {
            // 尝试加载自定义字体
            try {
                URL fontUrl = getClass().getClassLoader().getResource(options.getFontPath());
                if (fontUrl != null) {
                    chineseFont = BaseFont.createFont(fontUrl.getPath(), options.getFontEncoding(),
                            BaseFont.EMBEDDED);
                    log.info("Chinese font loaded successfully: {}", options.getFontPath());
                    return;
                }
            } catch (Exception e) {
                log.warn("Failed to load custom font from path: {}", options.getFontPath(), e);
            }

            // 回退到系统字体
            try {
                chineseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                log.warn("Using system font: STSong-Light");
            } catch (Exception e) {
                // 最后的回退选项
                chineseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
                log.warn("Using fallback font: Helvetica");
            }
        } catch (Exception e) {
            log.error("Failed to initialize font", e);
            throw new RuntimeException("Failed to initialize font", e);
        }
    }

    /**
     * 将HTML转换为PDF
     */
    public byte[] generatePdf(String html) throws Exception {
        return generatePdf(html, null);
    }

    /**
     * 将HTML转换为PDF
     */
    public byte[] generatePdf(String html, String cssContent) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generatePdf(html, cssContent, outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * 将HTML转换为PDF到输出流
     */
    public void generatePdf(String html, String cssContent, OutputStream outputStream) throws Exception {
        validateInputs(html, outputStream);

        Document document = null;
        PdfWriter writer = null;

        try {
            // 创建文档
            document = new Document(options.getPageSize(),
                    options.getMarginLeft(),
                    options.getMarginRight(),
                    options.getMarginTop(),
                    options.getMarginBottom());

            writer = PdfWriter.getInstance(document, outputStream);

            // 设置页眉页脚事件处理器
            HeaderFooterEventHandler eventHandler = null;
            if (options.getHeaderFooterOptions() != null) {
                eventHandler = new HeaderFooterEventHandler(options.getHeaderFooterOptions(), chineseFont);
                writer.setPageEvent(eventHandler);
            }

            // 设置文档元数据
            setDocumentInfo(document);

            document.open();

            // 处理HTML内容
            String processedHtml = preprocessHtml(html, cssContent);

            // 解析HTML并添加到文档
            parseHtmlContent(document, writer, processedHtml);

            // 添加水印
            if (options.getWatermarkOptions() != null) {
                addWatermarkToDocument(writer, document);
            }

            document.close();

            // 更新页脚总页数
            if (eventHandler != null && outputStream instanceof ByteArrayOutputStream) {
                updateTotalPages((ByteArrayOutputStream) outputStream, eventHandler, writer.getPageNumber() - 1);
            }

        } catch (Exception e) {
            log.error("Failed to generate PDF", e);
            if (document != null && document.isOpen()) {
                document.close();
            }
            throw new RuntimeException("PDF generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * 验证输入参数
     */
    private void validateInputs(String html, OutputStream outputStream) {
        if (StringUtils.isBlank(html)) {
            throw new IllegalArgumentException("HTML content cannot be empty");
        }
        if (outputStream == null) {
            throw new IllegalArgumentException("Output stream cannot be null");
        }
    }

    /**
     * 设置文档信息
     */
    private void setDocumentInfo(Document document) {
        if (StringUtils.isNotBlank(options.getTitle())) {
            document.addTitle(options.getTitle());
        }
        if (StringUtils.isNotBlank(options.getAuthor())) {
            document.addAuthor(options.getAuthor());
        }
        if (StringUtils.isNotBlank(options.getSubject())) {
            document.addSubject(options.getSubject());
        }
        document.addCreator("OpenPDF HTML Generator 2.2.4");
        document.addProducer();
        document.addCreationDate();
    }

    /**
     * 预处理HTML内容
     */
    private String preprocessHtml(String html, String cssContent) {
        StringBuilder processedHtml = new StringBuilder();

        // 确保HTML有完整的文档结构
        if (!html.trim().toLowerCase().startsWith("<!doctype") &&
                !html.trim().toLowerCase().startsWith("<html")) {
            processedHtml.append("<!DOCTYPE html>\n");
            processedHtml.append("<html>\n<head>\n");
            processedHtml.append("<meta charset=\"UTF-8\">\n");

            // 添加默认样式
            processedHtml.append("<style>\n");
            processedHtml.append(getDefaultCss());

            // 添加自定义CSS
            if (StringUtils.isNotBlank(cssContent)) {
                processedHtml.append("\n").append(cssContent);
            }

            // 添加自定义CSS属性
            if (!options.getCustomCssProperties().isEmpty()) {
                processedHtml.append("\nbody {\n");
                options.getCustomCssProperties().forEach((key, value) ->
                        processedHtml.append("  ").append(key).append(": ").append(value).append(";\n"));
                processedHtml.append("}\n");
            }

            processedHtml.append("</style>\n");
            processedHtml.append("</head>\n<body>\n");
            processedHtml.append(html);
            processedHtml.append("\n</body>\n</html>");
        } else {
            processedHtml.append(html);
            if (StringUtils.isNotBlank(cssContent) || !options.getCustomCssProperties().isEmpty()) {
                String additionalCss = buildAdditionalCss(cssContent);
                processedHtml = injectCss(processedHtml, additionalCss);
            }
        }

        return processedHtml.toString();
    }

    /**
     * 获取默认CSS样式
     */
    private String getDefaultCss() {
        return String.format("""
            * {
                box-sizing: border-box;
            }
            body {
                font-family: '%s', 'Microsoft YaHei', sans-serif;
                font-size: %.1fpt;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
            }
            table {
                border-collapse: collapse;
                width: 100%%;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
            }
            """, options.getDefaultFontFamily(), options.getDefaultFontSize());
    }

    /**
     * 构建额外的CSS
     */
    private String buildAdditionalCss(String cssContent) {
        StringBuilder css = new StringBuilder();

        if (StringUtils.isNotBlank(cssContent)) {
            css.append(cssContent);
        }

        if (!options.getCustomCssProperties().isEmpty()) {
            css.append("\nbody {\n");
            options.getCustomCssProperties().forEach((key, value) ->
                    css.append("  ").append(key).append(": ").append(value).append(";\n"));
            css.append("}\n");
        }

        return css.toString();
    }

    /**
     * 向HTML中注入CSS
     */
    private StringBuilder injectCss(StringBuilder html, String css) {
        if (StringUtils.isBlank(css)) {
            return html;
        }

        String htmlStr = html.toString();
        int headEnd = htmlStr.toLowerCase().indexOf("</head>");

        if (headEnd != -1) {
            StringBuilder result = new StringBuilder();
            result.append(htmlStr, 0, headEnd);
            result.append("<style>\n").append(css).append("\n</style>\n");
            result.append(htmlStr.substring(headEnd));
            return result;
        }

        return html;
    }

    /**
     * 解析HTML内容 - 使用OpenPDF 2.2.4新API
     */
    private void parseHtmlContent(Document document, PdfWriter writer, String html) throws Exception {
        try (StringReader reader = new StringReader(html)) {
            // 创建样式表
            Map<String, String> styleMap = new HashMap<>();
            styleMap.put("body", String.format("font-family: %s; font-size: %.1fpt;",
                    options.getDefaultFontFamily(), options.getDefaultFontSize()));

            // 设置基础URL
            if (StringUtils.isNotBlank(options.getBaseUrl())) {
                document.setHtmlStyleClass("baseurl", options.getBaseUrl());
            }

            // 使用HtmlParser解析HTML (OpenPDF 2.2.4 API)
            HtmlParser.parse(document, reader);
        }
    }

    /**
     * 为文档添加水印
     */
    private void addWatermarkToDocument(PdfWriter writer, Document document) {
        if (options.getWatermarkOptions() == null) {
            return;
        }

        PdfContentByte cb = writer.getDirectContentUnder();
        Rectangle pageSize = document.getPageSize();
        PdfGState gState = new PdfGState();
        gState.setFillOpacity(options.getWatermarkOptions().getOpacity());
        cb.setGState(gState);

        // 添加文本水印
        if (StringUtils.isNotBlank(options.getWatermarkOptions().getText())) {
            addTextWatermark(cb, pageSize, options.getWatermarkOptions());
        }

        // 添加图片水印
        if (StringUtils.isNotBlank(options.getWatermarkOptions().getImagePath())) {
            addImageWatermark(cb, pageSize, options.getWatermarkOptions());
        }
    }

    /**
     * 添加文本水印
     */
    private void addTextWatermark(PdfContentByte cb, Rectangle pageSize, PdfRenderOptions.WatermarkOptions options) {
        try {
            cb.saveState();

            // 解析颜色
            java.awt.Color color = parseColor(options.getColor());
            cb.setColorFill(new BaseColor(color.getRed(), color.getGreen(), color.getBlue()));

            // 设置字体
            cb.beginText();
            cb.setFontAndSize(chineseFont, options.getFontSize());

            // 计算水印位置
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 添加文字水印
            cb.showTextAligned(Element.ALIGN_CENTER, options.getText(),
                    position[0], position[1], options.getRotation());

            cb.endText();
            cb.restoreState();
        } catch (Exception e) {
            log.error("Failed to add text watermark", e);
        }
    }

    /**
     * 添加图片水印
     */
    private void addImageWatermark(PdfContentByte cb, Rectangle pageSize, PdfRenderOptions.WatermarkOptions options) {
        try {
            URL imageUrl = getClass().getClassLoader().getResource(options.getImagePath());
            if (imageUrl == null) {
                log.warn("Watermark image not found: {}", options.getImagePath());
                return;
            }

            Image image = Image.getInstance(imageUrl);
            if (image == null) {
                log.warn("Failed to create watermark image: {}", options.getImagePath());
                return;
            }

            cb.saveState();

            // 计算图片位置和大小
            float[] position = calculateWatermarkPosition(pageSize, options.getPosition());

            // 调整图片大小（保持比例，最大不超过页面的1/3）
            float maxWidth = pageSize.getWidth() / 3;
            float maxHeight = pageSize.getHeight() / 3;

            if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                image.scaleToFit(maxWidth, maxHeight);
            }

            // 设置图片位置和旋转
            image.setAbsolutePosition(
                    position[0] - image.getScaledWidth() / 2,
                    position[1] - image.getScaledHeight() / 2);
            image.setRotationDegrees(options.getRotation());

            // 添加图片
            cb.addImage(image);
            cb.restoreState();
        } catch (Exception e) {
            log.error("Failed to add image watermark", e);
        }
    }

    /**
     * 计算水印位置
     */
    private float[] calculateWatermarkPosition(Rectangle pageSize, String position) {
        float x = pageSize.getWidth() / 2;
        float y = pageSize.getHeight() / 2;

        switch (position.toLowerCase()) {
            case "top-left":
                x = pageSize.getLeft() + 50;
                y = pageSize.getTop() - 50;
                break;
            case "top-right":
                x = pageSize.getRight() - 50;
                y = pageSize.getTop() - 50;
                break;
            case "bottom-left":
                x = pageSize.getLeft() + 50;
                y = pageSize.getBottom() + 50;
                break;
            case "bottom-right":
                x = pageSize.getRight() - 50;
                y = pageSize.getBottom() + 50;
                break;
            default: // center
                break;
        }

        return new float[]{x, y};
    }

    /**
     * 解析颜色
     */
    private java.awt.Color parseColor(String colorStr) {
        try {
            if (colorStr.startsWith("#")) {
                return java.awt.Color.decode(colorStr);
            } else {
                // 尝试解析颜色名称
                return (java.awt.Color) java.awt.Color.class.getField(colorStr.toUpperCase()).get(null);
            }
        } catch (Exception e) {
            log.warn("Failed to parse color: {}, using default", colorStr);
            return java.awt.Color.LIGHT_GRAY;
        }
    }

    /**
     * 更新总页数
     */
    private void updateTotalPages(ByteArrayOutputStream outputStream, HeaderFooterEventHandler eventHandler, int totalPages) {
        if (!options.getHeaderFooterOptions().isShowPageNumbers()) {
            return;
        }

        try {
            eventHandler.setTotalPages(totalPages);

            // 重新生成包含正确总页数的PDF
            byte[] pdfData = outputStream.toByteArray();
            byte[] updatedPdf = updatePageNumbers(pdfData, totalPages);

            outputStream.reset();
            outputStream.write(updatedPdf);
        } catch (Exception e) {
            log.warn("Failed to update total pages", e);
        }
    }

    /**
     * 更新页码
     */
    private byte[] updatePageNumbers(byte[] originalPdf, int totalPages) throws Exception {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            PdfReader reader = new PdfReader(originalPdf);
            PdfStamper stamper = new PdfStamper(reader, output);

            // 更新每一页的页码
            for (int i = 1; i <= totalPages; i++) {
                PdfContentByte cb = stamper.getOverContent(i);
                Rectangle pageSize = reader.getPageSizeWithRotation(i);

                if (options.getHeaderFooterOptions() != null &&
                        options.getHeaderFooterOptions().isShowPageNumbers()) {
                    updatePageNumberOnPage(cb, pageSize, i, totalPages);
                }
            }

            stamper.close();
            reader.close();
            return output.toByteArray();
        }
    }

    /**
     * 更新页面上的页码
     */
    private void updatePageNumberOnPage(PdfContentByte cb, Rectangle pageSize, int pageNum, int totalPages) {
        try {
            Font font = new Font(chineseFont, options.getHeaderFooterOptions().getFontSize());
            String pageText = options.getHeaderFooterOptions().getPageNumberFormat()
                    .replace("{page}", String.valueOf(pageNum))
                    .replace("{total}", String.valueOf(totalPages));

            float footerY = pageSize.getBottom() + 20;
            float centerX = (pageSize.getLeft() + pageSize.getRight()) / 2;

            Phrase phrase = new Phrase(pageText, font);
            ColumnText.showTextAligned(cb, Element.ALIGN_CENTER, phrase, centerX, footerY, 0);
        } catch (Exception e) {
            log.error("Failed to update page number", e);
        }
    }

    /**
     * 静态工具方法：从HTML生成PDF
     */
    public static byte[] generatePdfFromHtml(String html) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(new PdfRenderOptions())) {
            return generator.generatePdf(html);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate PDF", e);
        }
    }

    /**
     * 静态工具方法：从HTML生成PDF（自定义选项）
     */
    public static byte[] generatePdfFromHtml(String html, PdfRenderOptions options) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
            return generator.generatePdf(html);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate PDF", e);
        }
    }

    /**
     * 静态工具方法：从HTML生成PDF（自定义CSS）
     */
    public static byte[] generatePdfFromHtml(String html, String css) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(new PdfRenderOptions())) {
            return generator.generatePdf(html, css);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate PDF", e);
        }
    }

    /**
     * 静态工具方法：从HTML生成PDF（自定义选项和CSS）
     */
    public static byte[] generatePdfFromHtml(String html, String css, PdfRenderOptions options) {
        try (HtmlToPdfGenerator generator = new HtmlToPdfGenerator(options)) {
            return generator.generatePdf(html, css);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate PDF", e);
        }
    }

    @Override
    public void close() {
        // 释放资源
    }
}

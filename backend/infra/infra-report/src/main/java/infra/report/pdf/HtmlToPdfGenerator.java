package infra.report.pdf;

import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;
import org.openpdf.pdf.ITextRenderer;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * HTML转PDF生成器
 */
@Slf4j
public class HtmlToPdfGenerator {

    /**
     * 将HTML转换为PDF
     *
     * @param htmlContent HTML内容
     * @param outputStream 输出流
     * @param options 渲染选项
     * @throws PdfException PDF处理异常
     */
    public static void generatePdf(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        if (Str.isEmpty(htmlContent)) {
            throw new PdfException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new PdfException("输出流不能为空");
        }

        // 使用默认选项
        if (options == null) {
            options = PdfRenderOptions.builder().build();
        }

        try {
            // 如果需要水印或页眉页脚，使用传统方式
            if (hasWatermarkOrHeaderFooter(options)) {
                generatePdfWithWatermarkAndHeaderFooter(htmlContent, outputStream, options);
            } else {
                // 纯HTML转PDF，使用OpenPDF HTML渲染器
                generatePdfWithRenderer(htmlContent, outputStream, options);
            }
        } catch (Exception e) {
            throw new PdfException("生成PDF失败", e);
        }
    }

    /**
     * 检查是否需要水印或页眉页脚
     */
    private static boolean hasWatermarkOrHeaderFooter(PdfRenderOptions options) {
        return (options.getWatermarkOptions() != null &&
                (!Str.isEmpty(options.getWatermarkOptions().getText()) ||
                        !Str.isEmpty(options.getWatermarkOptions().getImagePath()))) ||
                (options.getHeaderFooterOptions() != null);
    }

    /**
     * 使用OpenPDF HTML渲染器生成PDF（不带水印和页眉页脚）
     */
    private static void generatePdfWithRenderer(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        try {
            ITextRenderer renderer = new ITextRenderer();

            // 设置页边距
            renderer.getSharedContext().setPrint(true);
            renderer.getSharedContext().setInteractive(false);

            // 设置字体路径
            setupFonts(renderer, options);

            // 处理自定义CSS属性
            String processedHtml = preprocessHtml(htmlContent, options);

            // 设置基础URL
            if (!Str.isEmpty(options.getBaseUrl())) {
                renderer.setDocumentFromString(processedHtml, options.getBaseUrl());
            } else {
                renderer.setDocumentFromString(processedHtml);
            }

            renderer.layout();
            renderer.createPDF(outputStream);

        } catch (Exception e) {
            throw new PdfException("HTML渲染失败", e);
        }
    }

    /**
     * 预处理HTML，注入页边距和自定义CSS
     */
    private static String preprocessHtml(String htmlContent, PdfRenderOptions options) {
        StringBuilder cssBuilder = new StringBuilder();

        // 添加页边距CSS
        cssBuilder.append("@page { ")
                .append("margin-top: ").append(options.getMarginTop()).append("pt; ")
                .append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ")
                .append("margin-left: ").append(options.getMarginLeft()).append("pt; ")
                .append("margin-right: ").append(options.getMarginRight()).append("pt; ")
                .append("} ");

        // 添加默认字体CSS
        cssBuilder.append("body { ")
                .append("font-family: '").append(options.getDefaultFontFamily()).append("', sans-serif; ")
                .append("font-size: ").append(options.getDefaultFontSize()).append("pt; ")
                .append("} ");

        // 添加自定义CSS属性
        if (options.getCustomCssProperties() != null) {
            options.getCustomCssProperties().forEach((selector, rules) ->
                    cssBuilder.append(selector).append(" { ").append(rules).append(" } "));
        }

        String cssContent = cssBuilder.toString();

        // 如果HTML中没有head标签，添加一个
        if (!htmlContent.toLowerCase().contains("<head>")) {
            if (htmlContent.toLowerCase().contains("<html>")) {
                htmlContent = htmlContent.replaceFirst("(?i)<html[^>]*>",
                        "$0<head><style>" + cssContent + "</style></head>");
            } else {
                htmlContent = "<html><head><style>" + cssContent + "</style></head><body>" +
                        htmlContent + "</body></html>";
            }
        } else {
            // 在head标签中添加CSS
            htmlContent = htmlContent.replaceFirst("(?i)</head>",
                    "<style>" + cssContent + "</style></head>");
        }

        return htmlContent;
    }


    /**
     * 生成带水印和页眉页脚的PDF
     */
    private static void generatePdfWithWatermarkAndHeaderFooter(String htmlContent, OutputStream outputStream, PdfRenderOptions options) {
        try {
            // 先生成基础PDF到临时流
            ByteArrayOutputStream tempOutputStream = new ByteArrayOutputStream();
            generatePdfWithRenderer(htmlContent, tempOutputStream, options);

            // 读取生成的PDF
            byte[] basePdfBytes = tempOutputStream.toByteArray();
            PdfReader reader = new PdfReader(basePdfBytes);

            int totalPages = reader.getNumberOfPages();

            // 创建新的PDF写入器
            com.lowagie.text.Document document = new com.lowagie.text.Document();
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);

            // 设置页边距
            document.setMargins(options.getMarginLeft(), options.getMarginRight(),
                    options.getMarginTop(), options.getMarginBottom());

            // 设置页眉页脚事件处理器
            HeaderFooterEventHandler headerFooterHandler = null;
            if (options.getHeaderFooterOptions() != null) {
                BaseFont baseFont = getBaseFont(options);
                headerFooterHandler = new HeaderFooterEventHandler(options.getHeaderFooterOptions(), baseFont);
                // 预设总页数
                headerFooterHandler.setTotalPages(totalPages);
                writer.setPageEvent(headerFooterHandler);
            }

            document.open();

            PdfContentByte cb = writer.getDirectContent();

            // 复制原PDF的每一页并添加水印
            for (int i = 1; i <= totalPages; i++) {
                // 获取原页面尺寸
                Rectangle pageSize = reader.getPageSizeWithRotation(i);
                document.setPageSize(pageSize);
                document.newPage();

                // 导入原页面
                PdfImportedPage importedPage = writer.getImportedPage(reader, i);
                cb.addTemplate(importedPage, 0, 0);

                // 添加水印
                if (options.getWatermarkOptions() != null) {
                    BaseFont baseFont = getBaseFont(options);
                    WatermarkProcessor.addWatermark(writer, document, options.getWatermarkOptions(), baseFont);
                }
            }

            document.close();
            reader.close();

        } catch (Exception e) {
            throw new PdfException("生成带水印和页眉页脚的PDF失败", e);
        }
    }


    /**
     * 设置字体
     */
    private static void setupFonts(ITextRenderer renderer, PdfRenderOptions options) {
        try {
            String fontPath = determineFontPath(options);

            // 添加中文字体支持
            if (!Str.isEmpty(fontPath)) {
                Path fontDir = Paths.get(fontPath);
                if (Files.exists(fontDir) && Files.isDirectory(fontDir)) {
                    // 扫描字体目录
                    Files.walk(fontDir)
                            .filter(path -> {
                                String fileName = path.toString().toLowerCase();
                                return fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc");
                            })
                            .forEach(path -> {
                                try {
                                    // OpenPDF 2.2.4 支持的字体添加方式
                                    String fontFamily = extractFontFamily(path);
                                    renderer.getFontResolver().addFont(path.toString(), fontFamily, true);
                                    log.debug("添加字体: {} -> {}", fontFamily, path);
                                } catch (Exception e) {
                                    log.warn("添加字体失败: {}", path, e);
                                }
                            });
                }
            }

            // 添加默认字体和内置字体
            addDefaultFonts(renderer);

            // 设置默认字体族
            renderer.getFontResolver().setDefaultFontFamily(options.getDefaultFontFamily());

        } catch (Exception e) {
            log.warn("设置字体失败", e);
            // 仅添加默认字体
            addDefaultFonts(renderer);
        }
    }

    /**
     * 从字体文件路径提取字体族名称
     */
    private static String extractFontFamily(Path fontPath) {
        String fileName = fontPath.getFileName().toString();
        // 移除扩展名
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            fileName = fileName.substring(0, dotIndex);
        }
        return fileName;
    }

    /**
     * 添加默认字体
     */
    private static void addDefaultFonts(ITextRenderer renderer) {
        try {
            // OpenPDF 2.2.4 内置字体支持
            String[] defaultFonts = {
                    "serif", "sans-serif", "monospace"
            };

            for (String fontFamily : defaultFonts) {
                try {
                    renderer.getFontResolver().addFont(fontFamily, true);
                    log.debug("添加内置字体: {}", fontFamily);
                } catch (Exception e) {
                    log.debug("无法添加内置字体: {}", fontFamily);
                }
            }

            // 尝试添加系统中文字体
            addSystemChineseFonts(renderer);

        } catch (Exception e) {
            log.warn("添加默认字体失败", e);
        }
    }

    /**
     * 添加系统中文字体
     */
    private static void addSystemChineseFonts(ITextRenderer renderer) {
        // Windows 字体路径
        addFontsFromDirectory(renderer, "C:/Windows/Fonts");
        // Linux 字体路径
        addFontsFromDirectory(renderer, "/usr/share/fonts");
        addFontsFromDirectory(renderer, "/usr/local/share/fonts");
        // macOS 字体路径
        addFontsFromDirectory(renderer, "/System/Library/Fonts");
        addFontsFromDirectory(renderer, "/Library/Fonts");
    }

    /**
     * 从指定目录添加字体
     */
    private static void addFontsFromDirectory(ITextRenderer renderer, String dirPath) {
        try {
            Path fontDir = Paths.get(dirPath);
            if (Files.exists(fontDir) && Files.isDirectory(fontDir)) {
                Files.walk(fontDir, 2) // 限制深度避免过深搜索
                        .filter(path -> {
                            String fileName = path.toString().toLowerCase();
                            return (fileName.contains("sim") || fileName.contains("microsoft") ||
                                    fileName.contains("noto") || fileName.contains("source")) &&
                                    (fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc"));
                        })
                        .limit(10) // 限制数量避免加载过多字体
                        .forEach(path -> {
                            try {
                                String fontFamily = extractFontFamily(path);
                                renderer.getFontResolver().addFont(path.toString(), fontFamily, true);
                                log.debug("添加系统字体: {} -> {}", fontFamily, path);
                            } catch (Exception e) {
                                log.debug("添加系统字体失败: {}", path);
                            }
                        });
            }
        } catch (Exception e) {
            log.debug("扫描字体目录失败: {}", dirPath);
        }
    }


    /**
     * 添加默认字体
     */
    private static void addDefaultFonts(ITextRenderer renderer) {
        try {
            // 尝试添加常用中文字体
            String[] chineseFonts = {
                    "SimHei", "SimSun", "Microsoft YaHei", "NSimSun", "FangSong", "KaiTi"
            };

            for (String fontName : chineseFonts) {
                try {
                    renderer.getFontResolver().addFont(fontName, true);
                } catch (Exception e) {
                    log.debug("无法添加系统字体: {}", fontName);
                }
            }
        } catch (Exception e) {
            log.warn("添加默认字体失败", e);
        }
    }

    /**
     * 确定字体路径
     */
    private static String determineFontPath(PdfRenderOptions options) {
        // 1. 优先使用配置的外部字体路径
        if (!Str.isEmpty(options.getExternalFontPath())) {
            return options.getExternalFontPath();
        }

        // 2. 使用配置的字体路径
        if (!Str.isEmpty(options.getFontPath())) {
            return options.getFontPath();
        }

        // 3. 使用环境变量
        String envFontPath = System.getenv("FONT_PATH");
        if (!Str.isEmpty(envFontPath)) {
            return envFontPath;
        }

        // 4. 使用系统属性
        String sysFontPath = System.getProperty("font.path");
        if (!Str.isEmpty(sysFontPath)) {
            return sysFontPath;
        }

        // 5. 使用默认路径
        return "/app/fonts";
    }

    /**
     * 获取基础字体
     */
    private static BaseFont getBaseFont(PdfRenderOptions options) throws Exception {
        String fontPath = determineFontPath(options);

        // 尝试加载配置的字体
        if (!Str.isEmpty(fontPath)) {
            try {
                Path fontFile = Paths.get(fontPath, options.getDefaultFontFamily() + ".ttf");
                if (Files.exists(fontFile)) {
                    return BaseFont.createFont(fontFile.toString(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                }
            } catch (Exception e) {
                log.debug("加载自定义字体失败: {}", e.getMessage());
            }
        }

        // 使用内置字体
        try {
            return BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            // 最后使用默认字体
            return BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
        }
    }

    /**
     * 从文件生成PDF
     */
    public static void generatePdfFromFile(String htmlFilePath, String outputFilePath, PdfRenderOptions options) {
        try {
            String htmlContent = Files.readString(Paths.get(htmlFilePath));

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                Path htmlPath = Paths.get(htmlFilePath);
                String baseUrl = htmlPath.getParent().toUri().toString();
                options = options.toBuilder().baseUrl(baseUrl).build();
            }

            try (FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {
                generatePdf(htmlContent, outputStream, options);
            }

        } catch (Exception e) {
            throw new PdfException("从文件生成PDF失败", e);
        }
    }

    /**
     * 生成PDF到字节数组
     */
    public static byte[] generatePdfToBytes(String htmlContent, PdfRenderOptions options) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            generatePdf(htmlContent, outputStream, options);
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new PdfException("生成PDF字节数组失败", e);
        }
    }

    /**
     * 生成PDF到字节数组（从文件）
     */
    public static byte[] generatePdfFromFileToBytes(String htmlFilePath, PdfRenderOptions options) {
        try {
            String htmlContent = Files.readString(Paths.get(htmlFilePath));

            // 设置基础URL为HTML文件所在目录
            if (options != null && Str.isEmpty(options.getBaseUrl())) {
                Path htmlPath = Paths.get(htmlFilePath);
                String baseUrl = htmlPath.getParent().toUri().toString();
                options = options.toBuilder().baseUrl(baseUrl).build();
            }

            return generatePdfToBytes(htmlContent, options);
        } catch (Exception e) {
            throw new PdfException("从文件生成PDF字节数组失败", e);
        }
    }
}


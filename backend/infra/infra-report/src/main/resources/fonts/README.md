# 中文字体文件说明

本目录用于存放PDF生成所需的中文字体文件。

## 推荐字体

请将以下字体文件放置在此目录下：

### 基础中文字体
- `SimHei.ttf` - 黑体（Windows系统字体）
- `SimSun.ttf` - 宋体（Windows系统字体）

### 现代中文字体
- `NotoSansCJK-Regular.ttc` - Google Noto Sans CJK 字体
- `SourceHanSans-Regular.ttc` - Adobe Source Han Sans 字体

### 其他推荐字体
- `PingFang.ttc` - 苹果苹方字体
- `MicrosoftYaHei.ttf` - 微软雅黑
- `DroidSansFallback.ttf` - Android 默认中文字体

## 字体获取方式

### 1. 系统字体
- Windows: `C:\Windows\Fonts\`
- macOS: `/System/Library/Fonts/` 或 `/Library/Fonts/`
- Linux: `/usr/share/fonts/` 或 `~/.fonts/`

### 2. 开源字体下载
- Noto Sans CJK: https://github.com/googlefonts/noto-cjk
- Source Han Sans: https://github.com/adobe-fonts/source-han-sans

### 3. 在线字体服务
- Google Fonts
- Adobe Fonts

## 使用说明

1. 将字体文件复制到此目录
2. 字体会在应用启动时自动注册
3. 在CSS中可以通过字体名称引用

## 注意事项

- 字体文件较大，建议不要提交到版本控制系统
- 确保字体文件的版权合规
- 生产环境部署时需要确保字体文件存在

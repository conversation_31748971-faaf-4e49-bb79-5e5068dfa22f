# 字体文件目录

此目录用于存放PDF生成所需的中文字体文件。

## 字体文件放置说明

请将以下中文字体文件放置在此目录下：

### 推荐字体
- `simhei.ttf` - 黑体（推荐，对应defaultFontFamily="simhei"）
- `simsun.ttc` - 宋体
- `SimHei.ttf` - 黑体（大写版本）
- `SimSun.ttf` - 宋体（大写版本）

### 可选字体
- `NotoSansCJK-Regular.ttc` - 思源黑体
- `SourceHanSans-Regular.ttc` - 思源黑体

## 字体获取方式

1. **从系统复制**：
   - Windows: `C:\Windows\Fonts\`
   - Linux: `/usr/share/fonts/`
   - macOS: `/System/Library/Fonts/`

2. **下载开源字体**：
   - 思源黑体：https://github.com/adobe-fonts/source-han-sans
   - Noto字体：https://fonts.google.com/noto

## 测试字体功能

访问以下端点测试字体功能：
- `/test/font-debug` - 字体调试信息
- `/test/optimized-font-test` - 字体效果测试

## 故障排除

如果字体不显示：
1. 确认字体文件存在于此目录
2. 检查文件名大小写
3. 确认文件格式（.ttf, .otf, .ttc）
4. 查看应用日志中的字体加载信息

## 注意事项

1. 字体文件会增加JAR包大小
2. 确保字体文件的版权合规
3. 建议只包含必需的字体文件

package infra.report.pdf;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;

/**
 * HTML转PDF生成器测试
 */
class HtmlToPdfGeneratorTest {

    @TempDir
    Path tempDir;

    @Test
    void testBasicHtmlToPdf() throws IOException {
        String html = """
            <html>
            <head>
                <meta charset="UTF-8">
                <title>测试文档</title>
            </head>
            <body>
                <h1>这是一个测试标题</h1>
                <p>这是一段测试文本，包含中文字符。</p>
                <table>
                    <tr>
                        <th>姓名</th>
                        <th>年龄</th>
                        <th>城市</th>
                    </tr>
                    <tr>
                        <td>张三</td>
                        <td>25</td>
                        <td>北京</td>
                    </tr>
                    <tr>
                        <td>李四</td>
                        <td>30</td>
                        <td>上海</td>
                    </tr>
                </table>
            </body>
            </html>
            """;

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html);
        
        // 保存到临时文件
        Path outputFile = tempDir.resolve("test-basic.pdf");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            fos.write(pdfBytes);
        }
        
        System.out.println("基础PDF生成成功: " + outputFile);
    }

    @Test
    void testHtmlToPdfWithWatermark() throws IOException {
        String html = """
            <html>
            <head>
                <meta charset="UTF-8">
                <title>带水印的测试文档</title>
            </head>
            <body>
                <h1>带水印的PDF文档</h1>
                <p>这个文档包含文本水印。</p>
                <div style="height: 500px;">
                    <p>这里是一些内容...</p>
                </div>
            </body>
            </html>
            """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .watermarkOptions(PdfRenderOptions.WatermarkOptions.builder()
                        .text("机密文档")
                        .color("#FF0000")
                        .opacity(0.3f)
                        .rotation(45f)
                        .fontSize(48f)
                        .position("center")
                        .build())
                .build();

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, options);
        
        // 保存到临时文件
        Path outputFile = tempDir.resolve("test-watermark.pdf");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            fos.write(pdfBytes);
        }
        
        System.out.println("带水印PDF生成成功: " + outputFile);
    }

    @Test
    void testHtmlToPdfWithHeaderFooter() throws IOException {
        String html = """
            <html>
            <head>
                <meta charset="UTF-8">
                <title>带页眉页脚的测试文档</title>
            </head>
            <body>
                <h1>带页眉页脚的PDF文档</h1>
                <p>这个文档包含页眉和页脚。</p>
                <div style="height: 800px;">
                    <p>这里是一些内容...</p>
                    <p>更多内容...</p>
                </div>
                <p>第二页内容</p>
            </body>
            </html>
            """;

        PdfRenderOptions options = PdfRenderOptions.builder()
                .headerFooterOptions(PdfRenderOptions.HeaderFooterOptions.builder()
                        .headerCenter("公司内部文档")
                        .footerLeft("机密")
                        .footerRight("2024年")
                        .showPageNumbers(true)
                        .pageNumberFormat("第 {page} 页，共 {total} 页")
                        .fontSize(10f)
                        .build())
                .build();

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, options);
        
        // 保存到临时文件
        Path outputFile = tempDir.resolve("test-header-footer.pdf");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            fos.write(pdfBytes);
        }
        
        System.out.println("带页眉页脚PDF生成成功: " + outputFile);
    }

    @Test
    void testHtmlToPdfWithCustomCss() throws IOException {
        String html = """
            <html>
            <head>
                <meta charset="UTF-8">
                <title>自定义样式测试文档</title>
            </head>
            <body>
                <h1 class="title">自定义样式标题</h1>
                <p class="content">这是带有自定义样式的内容。</p>
                <div class="box">
                    <p>这是一个带边框的盒子。</p>
                </div>
            </body>
            </html>
            """;

        String css = """
            .title {
                color: #2c3e50;
                text-align: center;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
            }
            .content {
                font-size: 14pt;
                line-height: 1.8;
                color: #34495e;
            }
            .box {
                border: 2px solid #e74c3c;
                padding: 20px;
                margin: 20px 0;
                background-color: #f8f9fa;
                border-radius: 5px;
            }
            """;

        byte[] pdfBytes = HtmlToPdfGenerator.generatePdfFromHtml(html, css);
        
        // 保存到临时文件
        Path outputFile = tempDir.resolve("test-custom-css.pdf");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            fos.write(pdfBytes);
        }
        
        System.out.println("自定义样式PDF生成成功: " + outputFile);
    }
}

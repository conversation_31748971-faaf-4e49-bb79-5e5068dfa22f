<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>infra</groupId>
        <artifactId>infra</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>infra-report</artifactId>
    <description>Excel、Word、PDF、报表等相关</description>

    <dependencies>
        <!-- Spring/Web -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

        <!-- Excel -->
        <dependency>
            <groupId>cn.idev.excel</groupId>
            <artifactId>fastexcel</artifactId>
        </dependency>

        <!-- 二维码/条码 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>

        <!-- PDF -->
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-svg-support</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-mathml-support</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-slf4j</artifactId>
            <version>${openhtmltopdf.version}</version>
        </dependency>

        <!-- 其它依赖 -->
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>${mybatis-plus.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>

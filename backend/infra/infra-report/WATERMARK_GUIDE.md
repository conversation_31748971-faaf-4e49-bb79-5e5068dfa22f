# 循环水印功能使用指南

## 概述

PDF生成服务现在支持两种水印模式：
1. **单个水印**：在页面指定位置显示一个水印
2. **循环水印**：在整个页面重复显示水印，形成网格效果

## 配置参数

### 基础水印参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `text` | String | - | 水印文本内容 |
| `imagePath` | String | - | 图片水印路径 |
| `color` | String | "#CCCCCC" | 水印颜色（十六进制） |
| `opacity` | float | 0.3f | 透明度（0.0-1.0） |
| `rotation` | float | 45f | 旋转角度（度） |
| `fontSize` | float | 48f | 字体大小 |
| `position` | String | "center" | 单个水印位置 |

### 循环水印参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `repeatWatermark` | boolean | false | 是否启用循环水印 |
| `horizontalSpacing` | float | 200f | 水平间距（像素） |
| `verticalSpacing` | float | 150f | 垂直间距（像素） |
| `density` | float | 1.0f | 稀疏度控制 |

## 稀疏度说明

`density` 参数控制水印的密集程度：

- **1.0**：正常密度，使用原始间距
- **0.5**：稀疏模式，间距扩大2倍，水印更稀疏
- **2.0**：密集模式，间距缩小2倍，水印更密集
- **0.3**：超稀疏模式，间距扩大约3.3倍
- **3.0**：超密集模式，间距缩小3倍

## 使用示例

### 1. 单个水印（默认）

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .watermarkOptions(WatermarkOptions.builder()
        .text("CONFIDENTIAL")
        .color("#FF0000")
        .opacity(0.3f)
        .rotation(-45f)
        .fontSize(48f)
        .position("center")
        .build())
    .build();
```

### 2. 标准循环水印

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .watermarkOptions(WatermarkOptions.builder()
        .text("DRAFT")
        .color("#CCCCCC")
        .opacity(0.3f)
        .rotation(-45f)
        .fontSize(36f)
        .repeatWatermark(true)           // 启用循环水印
        .horizontalSpacing(200f)         // 水平间距
        .verticalSpacing(150f)           // 垂直间距
        .density(1.0f)                   // 正常密度
        .build())
    .build();
```

### 3. 密集循环水印

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .watermarkOptions(WatermarkOptions.builder()
        .text("TOP SECRET")
        .color("#FF6B6B")
        .opacity(0.2f)
        .rotation(-30f)
        .fontSize(24f)
        .repeatWatermark(true)
        .horizontalSpacing(150f)
        .verticalSpacing(100f)
        .density(2.0f)                   // 密集2倍
        .build())
    .build();
```

### 4. 稀疏循环水印

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .watermarkOptions(WatermarkOptions.builder()
        .text("SAMPLE")
        .color("#999999")
        .opacity(0.4f)
        .rotation(45f)
        .fontSize(48f)
        .repeatWatermark(true)
        .horizontalSpacing(300f)
        .verticalSpacing(200f)
        .density(0.5f)                   // 稀疏0.5倍
        .build())
    .build();
```

### 5. 图片循环水印

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .watermarkOptions(WatermarkOptions.builder()
        .imagePath("/path/to/logo.png")
        .opacity(0.2f)
        .rotation(0f)
        .repeatWatermark(true)
        .horizontalSpacing(250f)
        .verticalSpacing(200f)
        .density(1.0f)
        .build())
    .build();
```

## 测试端点

系统提供了多个测试端点来验证水印功能：

### 基础测试
- `GET /test/pdf` - 基础PDF生成测试（包含单个水印）
- `GET /test/watermark-test` - 水印和页眉页脚测试

### 循环水印测试
- `GET /test/repeat-watermark-test` - 标准循环水印测试
- `GET /test/dense-watermark-test` - 密集水印测试（density=2.0）
- `GET /test/sparse-watermark-test` - 稀疏水印测试（density=0.5）

## 最佳实践

### 1. 文本水印建议

- **机密文档**：使用红色，中等透明度，倾斜显示
  ```java
  .text("CONFIDENTIAL").color("#FF0000").opacity(0.3f).rotation(-45f)
  ```

- **草稿文档**：使用灰色，低透明度，密集显示
  ```java
  .text("DRAFT").color("#CCCCCC").opacity(0.2f).density(1.5f)
  ```

- **样本文档**：使用浅灰色，高透明度，稀疏显示
  ```java
  .text("SAMPLE").color("#999999").opacity(0.4f).density(0.5f)
  ```

### 2. 间距设置建议

- **A4纸张**：水平间距200-300px，垂直间距150-200px
- **小字体**（<30px）：可以使用较小间距（150px/100px）
- **大字体**（>50px）：建议使用较大间距（300px/250px）

### 3. 密度选择建议

- **高安全性文档**：density=1.5-2.0（密集）
- **普通文档**：density=1.0（标准）
- **阅读友好文档**：density=0.5-0.7（稀疏）

### 4. 透明度建议

- **不影响阅读**：opacity=0.1-0.3
- **明显但不干扰**：opacity=0.3-0.5
- **强调效果**：opacity=0.5-0.7

## 注意事项

1. **性能考虑**：循环水印会增加PDF文件大小和生成时间
2. **密度限制**：过高的密度（>3.0）可能影响性能
3. **间距最小值**：建议间距不小于50px，避免水印重叠
4. **图片大小**：图片水印会自动缩放以适应间距
5. **旋转角度**：建议使用-45°、0°、45°等常见角度

## 故障排除

### 水印不显示
1. 检查 `repeatWatermark` 是否设置为 `true`
2. 确认 `text` 或 `imagePath` 不为空
3. 检查 `opacity` 是否过低

### 水印过密或过稀
1. 调整 `density` 参数
2. 修改 `horizontalSpacing` 和 `verticalSpacing`
3. 检查字体大小是否合适

### 性能问题
1. 降低 `density` 值
2. 增加间距参数
3. 减小字体大小或图片尺寸

# PDF字体使用指南

## 概述

PDF生成服务现在支持内置资源字体，确保在各种部署环境下都能正确显示中文内容。

## 字体加载策略

### 1. 字体加载优先级

1. **资源字体**：`src/main/resources/fonts/` 目录下的字体文件
2. **外部字体**：通过配置指定的外部字体目录
3. **系统默认字体**：Helvetica等系统字体

### 2. 字体加载逻辑

```java
// 如果配置了外部字体目录
if (externalFontPath != null) {
    loadResourceFonts();     // 加载资源字体
    loadExternalFonts();     // 加载外部字体
} else {
    loadResourceFonts();     // 只加载资源字体
}
```

## 配置方式

### 1. 使用资源字体（推荐）

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .defaultFontFamily("simhei")  // 对应 SimHei.ttf
    .build();
```

### 2. 使用外部字体目录

```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .fontPath("/path/to/external/fonts")
    .defaultFontFamily("simhei")
    .build();
```

### 3. 环境变量配置

```bash
export FONT_PATH=/path/to/external/fonts
```

## 支持的字体文件

### 资源字体目录结构
```
src/main/resources/fonts/
├── SimHei.ttf          # 黑体（推荐）
├── SimSun.ttf          # 宋体
├── NotoSansCJK-Regular.ttc    # 思源黑体
├── SourceHanSans-Regular.ttc  # 思源黑体
├── simhei.ttf          # 小写版本
└── simsun.ttf          # 小写版本
```

### 支持的文件格式
- `.ttf` - TrueType字体
- `.otf` - OpenType字体
- `.ttc` - TrueType集合字体

## 部署场景

### 1. JAR包部署
- 字体文件打包在JAR中
- 运行时自动提取到临时目录
- 无需额外配置

### 2. Docker部署
- 字体文件包含在镜像中
- 开箱即用，无需挂载字体目录
- 支持外部字体目录挂载扩展

### 3. 开发环境
- 直接从resources目录加载
- 支持热重载
- 便于调试和测试

## 使用示例

### 1. 基础使用
```java
// 使用默认资源字体
byte[] pdf = PdfUtil.htmlToPdf(html);
```

### 2. 指定字体
```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .defaultFontFamily("simhei")
    .build();
byte[] pdf = PdfUtil.htmlToPdf(html, options);
```

### 3. 页眉页脚字体
```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .defaultFontFamily("simhei")
    .headerFooterOptions(HeaderFooterOptions.builder()
        .headerCenter("中文标题")
        .footerLeft("中文页脚")
        .build())
    .build();
```

### 4. 水印字体
```java
PdfRenderOptions options = PdfRenderOptions.builder()
    .defaultFontFamily("simhei")
    .watermarkOptions(WatermarkOptions.builder()
        .text("机密文档")
        .build())
    .build();
```

## 测试端点

- `/test/pdf` - 基础PDF生成测试
- `/test/resource-font-test` - 资源字体专门测试
- `/test/font-cache-test` - 字体缓存性能测试

## 性能优化

### 1. 字体缓存
- 资源字体只提取一次到临时目录
- 避免重复的文件系统操作
- 提高PDF生成性能

### 2. 临时文件管理
- 字体文件提取到系统临时目录
- JVM退出时自动清理
- 不占用永久存储空间

### 3. 内存优化
- 按需加载字体文件
- 流式处理，避免大文件全部加载到内存
- 合理的资源释放机制

## 故障排除

### 1. 中文显示为方块
- 检查字体文件是否存在于resources/fonts/目录
- 确认defaultFontFamily配置正确
- 查看日志中的字体加载信息

### 2. 字体文件未找到
```
WARN - 未找到资源字体文件: simhei
```
- 确认字体文件名大小写
- 检查文件扩展名（.ttf, .otf, .ttc）
- 验证文件是否在正确的目录

### 3. 外部字体目录无效
```
WARN - 外部字体目录不存在: /path/to/fonts
```
- 检查路径是否正确
- 确认目录权限
- 验证环境变量设置

## 最佳实践

1. **字体选择**：优先使用SimHei.ttf，兼容性最好
2. **文件命名**：使用小写文件名，避免大小写问题
3. **版权合规**：确保使用的字体文件符合版权要求
4. **文件大小**：只包含必需的字体，控制JAR包大小
5. **测试验证**：在目标部署环境中测试字体效果

## 注意事项

1. 字体文件会增加JAR包大小
2. 临时文件在系统临时目录中创建
3. 确保有足够的临时目录空间
4. 字体文件的版权和许可证合规性
